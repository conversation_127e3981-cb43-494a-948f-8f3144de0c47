@extends('layouts.app')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">Edit Polling Station</h5>
                </div>
                <div class="card-body">
                    @if(session('status'))
                        <div class="alert alert-danger">
                            {{ session('status') }}
                        </div>
                    @endif
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('polling_stations.update',$pollingStation->id) }}" method="post">
                        @csrf
                        @method('PATCH')
                        
                        <div class="mb-3">
                            <label for="name" class="form-label fw-bold">
                                <i class="bi bi-building me-1"></i>Polling Station Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" id="name" name="name" value="{{ old('name', $pollingStation->name) }}" class="form-control @error('name') is-invalid @enderror" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Administrative Divisions -->
                        <div class="card mb-3 border-info">
                            <div class="card-header bg-info bg-opacity-10 py-2">
                                <h6 class="mb-0">
                                    <i class="bi bi-geo-alt me-2"></i>Administrative Location
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- District and County -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="district" class="form-label fw-bold">
                                            District <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" name="district" id="district"
                                               class="form-control @error('district') is-invalid @enderror"
                                               placeholder="Enter district"
                                               value="{{ old('district', $pollingStation->district) }}" required>
                                        @error('district')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6">
                                        <label for="county" class="form-label fw-bold">
                                            County <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" name="county" id="county"
                                               class="form-control @error('county') is-invalid @enderror"
                                               placeholder="Enter county"
                                               value="{{ old('county', $pollingStation->county) }}" required>
                                        @error('county')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Subcounty and Parish -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="subcounty" class="form-label fw-bold">
                                            Subcounty <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" name="subcounty" id="subcounty"
                                               class="form-control @error('subcounty') is-invalid @enderror"
                                               placeholder="Enter subcounty"
                                               value="{{ old('subcounty', $pollingStation->subcounty) }}" required>
                                        @error('subcounty')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6">
                                        <label for="parish" class="form-label fw-bold">
                                            Parish <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" name="parish" id="parish"
                                               class="form-control @error('parish') is-invalid @enderror"
                                               placeholder="Enter parish"
                                               value="{{ old('parish', $pollingStation->parish) }}" required>
                                        @error('parish')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Village and Constituency -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="village" class="form-label fw-bold">
                                            Village <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" name="village" id="village"
                                               class="form-control @error('village') is-invalid @enderror"
                                               placeholder="Enter village"
                                               value="{{ old('village', $pollingStation->village) }}" required>
                                        @error('village')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6">
                                        <label for="constituency" class="form-label fw-bold">
                                            Constituency
                                        </label>
                                        <input type="text" name="constituency" id="constituency"
                                               class="form-control @error('constituency') is-invalid @enderror"
                                               placeholder="Enter constituency (optional)"
                                               value="{{ old('constituency', $pollingStation->constituency) }}">
                                        @error('constituency')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-4 border-info">
                            <div class="card-header bg-info bg-opacity-10">
                                <h5 class="mb-0">
                                    <i class="bi bi-geo-alt me-2"></i>
                                    Polling Station Coordinates
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Address Lookup Section -->
                                <div class="mb-4 border-bottom pb-4">
                                    <label for="address-lookup" class="form-label fw-bold">Find Coordinates by Address</label>
                                    <div class="input-group mb-3">
                                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                                        <input type="text" id="address-lookup" class="form-control" placeholder="Enter address, city, or location">
                                        <button class="btn btn-primary" type="button" id="geocode-btn">
                                            <i class="bi bi-geo-alt me-1"></i> Find
                                        </button>
                                    </div>
                                    <div id="geocode-results" class="d-none alert alert-success">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <i class="bi bi-check-circle me-2"></i>
                                                <span id="geocode-address">Address found</span>
                                            </div>
                                            <button type="button" id="apply-coordinates" class="btn btn-sm btn-success">
                                                <i class="bi bi-check me-1"></i> Use These Coordinates
                                            </button>
                                        </div>
                                        <div class="mt-2 small">
                                            <strong>Latitude:</strong> <span id="geocode-lat"></span>, 
                                            <strong>Longitude:</strong> <span id="geocode-lng"></span>
                                        </div>
                                    </div>
                                    <div id="geocode-error" class="d-none alert alert-danger">
                                        <i class="bi bi-exclamation-triangle me-2"></i>
                                        <span id="error-message">Could not find coordinates for this address.</span>
                                    </div>
                                </div>
                                
                                <!-- Manual Coordinates Input -->
                                <h6 class="mb-3">Manual Coordinates Input</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="latitude" class="form-label">Latitude</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-geo"></i></span>
                                                <input type="number" step="any" id="latitude" name="latitude" value="{{ old('latitude', $pollingStation->latitude) }}" class="form-control @error('latitude') is-invalid @enderror" placeholder="e.g. 0.3476">
                                                @error('latitude')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            <small class="form-text text-muted">Decimal format (e.g. 0.3476)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="longitude" class="form-label">Longitude</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-geo"></i></span>
                                                <input type="number" step="any" id="longitude" name="longitude" value="{{ old('longitude', $pollingStation->longitude) }}" class="form-control @error('longitude') is-invalid @enderror" placeholder="e.g. 32.5825">
                                                @error('longitude')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            <small class="form-text text-muted">Decimal format (e.g. 32.5825)</small>
                                        </div>
                                    </div>
                                </div>
                                @error('coordinates')
                                <div class="alert alert-danger">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    {{ $message }}
                                </div>
                                @enderror
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    Adding coordinates will allow the polling station to be displayed on maps and used for location-based features.
                                    <strong>Note:</strong> Coordinates must be within Uganda's borders.
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('polling_stations.index') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-warning text-white">
                                <i class="bi bi-save me-1"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Geocoding JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const geocodeBtn = document.getElementById('geocode-btn');
        const addressInput = document.getElementById('address-lookup');
        const resultsDiv = document.getElementById('geocode-results');
        const errorDiv = document.getElementById('geocode-error');
        const latitudeInput = document.getElementById('latitude');
        const longitudeInput = document.getElementById('longitude');
        const applyBtn = document.getElementById('apply-coordinates');
        
        let foundLat = null;
        let foundLng = null;
        
        // Function to check if coordinates are within Uganda
        function isWithinUganda(lat, lng) {
            // Uganda's approximate bounding box
            const ugandaBounds = {
                minLat: -1.5, // Southern border
                maxLat: 4.3,  // Northern border
                minLng: 29.5, // Western border
                maxLng: 35.0  // Eastern border
            };
            
            return lat >= ugandaBounds.minLat && 
                   lat <= ugandaBounds.maxLat && 
                   lng >= ugandaBounds.minLng && 
                   lng <= ugandaBounds.maxLng;
        }
        
        // Function to geocode an address using Nominatim API
        async function geocodeAddress(address) {
            // Reset UI
            resultsDiv.classList.add('d-none');
            errorDiv.classList.add('d-none');
            
            try {
                // Use Nominatim API (free and open-source)
                // Add viewbox parameter to prioritize Uganda results
                const ugandaViewbox = '29.5,4.3,35.0,-1.5'; // minLng,maxLat,maxLng,minLat
                const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&viewbox=${ugandaViewbox}&bounded=1&countrycodes=ug`);
                const data = await response.json();
                
                if (data && data.length > 0) {
                    // Get the first result
                    const result = data[0];
                    foundLat = parseFloat(result.lat);
                    foundLng = parseFloat(result.lon);
                    
                    // Check if the coordinates are within Uganda
                    if (isWithinUganda(foundLat, foundLng)) {
                        // Display the result
                        document.getElementById('geocode-address').textContent = result.display_name;
                        document.getElementById('geocode-lat').textContent = foundLat.toFixed(7);
                        document.getElementById('geocode-lng').textContent = foundLng.toFixed(7);
                        resultsDiv.classList.remove('d-none');
                    } else {
                        // Location is not in Uganda
                        document.getElementById('error-message').textContent = 'The found location is not within Uganda. Please enter a location in Uganda.';
                        errorDiv.classList.remove('d-none');
                    }
                } else {
                    // No results found
                    document.getElementById('error-message').textContent = 'No locations found in Uganda for this address.';
                    errorDiv.classList.remove('d-none');
                }
            } catch (error) {
                console.error('Geocoding error:', error);
                document.getElementById('error-message').textContent = 'Error connecting to geocoding service. Please try again.';
                errorDiv.classList.remove('d-none');
            }
        }
        
        // Event listener for geocode button
        geocodeBtn.addEventListener('click', function() {
            const address = addressInput.value.trim();
            if (address) {
                geocodeAddress(address);
            } else {
                document.getElementById('error-message').textContent = 'Please enter an address to search.';
                errorDiv.classList.remove('d-none');
            }
        });
        
        // Event listener for apply coordinates button
        applyBtn.addEventListener('click', function() {
            if (foundLat !== null && foundLng !== null) {
                latitudeInput.value = foundLat;
                longitudeInput.value = foundLng;
                resultsDiv.classList.add('d-none');
                
                // Show success message
                const successAlert = document.createElement('div');
                successAlert.className = 'alert alert-success mt-3';
                successAlert.innerHTML = '<i class="bi bi-check-circle me-2"></i>Coordinates applied successfully!';
                
                // Insert after the input fields
                const coordsRow = document.querySelector('.row');
                coordsRow.parentNode.insertBefore(successAlert, coordsRow.nextSibling);
                
                // Remove after 3 seconds
                setTimeout(() => {
                    successAlert.remove();
                }, 3000);
            }
        });
        
        // Also trigger geocoding when pressing Enter in the address input
        addressInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                geocodeBtn.click();
            }
        });
    });
</script>
@endsection
