@extends('layouts.app')

@section('content')
<style>
    /* Modern Dashboard Styles */
    .dashboard-container {
        padding: 1.5rem 0;
    }
    
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0;
    }
    
    .filter-bar {
        background: #fff;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    
    .stats-row {
        margin-bottom: 1.5rem;
    }
    
    .stats-card {
        background: #fff;
        border-radius: 12px;
        padding: 1.25rem;
        height: 100%;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        color: white;
        font-size: 1.5rem;
    }
    
    .stats-number {
        font-size: 1.75rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .station-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .station-card {
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s;
        position: relative;
    }
    
    .station-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    
    .station-header {
        padding: 1rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        background: #f8f9fa;
    }
    
    .station-body {
        padding: 1rem;
    }
    
    .station-footer {
        padding: 1rem;
        border-top: 1px solid rgba(0,0,0,0.05);
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .station-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }
    
    .station-subtitle {
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .status-badge {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .status-submitted {
        background: rgba(25, 135, 84, 0.1);
        color: #198754;
    }
    
    .status-pending {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }
    
    .agent-list {
        max-height: 150px;
        overflow-y: auto;
    }
    
    .agent-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .agent-item:last-child {
        border-bottom: none;
    }
    
    .agent-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
    }
    
    .agent-name {
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .agent-phone {
        font-size: 0.75rem;
        color: #6c757d;
    }
    
    .btn-action {
        padding: 0.375rem 0.75rem;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        border: none;
        color: white;
    }
    
    .pagination-custom {
        margin-top: 1.5rem;
        display: flex;
        justify-content: center;
    }
    
    .page-link-custom {
        color: #FFA500;
        border: none;
        padding: 0.5rem 0.75rem;
        margin: 0 0.25rem;
        border-radius: 6px;
    }
    
    .page-link-custom:hover {
        background: rgba(255, 165, 0, 0.1);
    }
    
    .page-item.active .page-link-custom {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        color: white;
    }
    
    .map-container {
        height: 400px;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 1.5rem;
    }
    
    /* Responsive adjustments */
    @media (max-width: 992px) {
        .station-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        }
    }
    
    @media (max-width: 768px) {
        .station-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<div class="container dashboard-container">
    <!-- Page Header -->
    <div class="section-header">
        <div>
            <h1 class="section-title">Polling Stations</h1>
            <p class="text-muted">Manage and monitor all polling stations</p>
        </div>
        <button type="button" class="btn btn-primary-custom" data-bs-toggle="modal" data-bs-target="#add_pollingstation">
            <i class="bi bi-plus-circle me-1"></i> Add Station
        </button>
    </div>
    
    <!-- Filter and Search Bar -->
    <div class="filter-bar">
        <div class="row g-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text bg-light border-0">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control border-0" id="searchInput" placeholder="Search stations...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="constituencyFilter">
                    <option value="">All Constituencies</option>
                    <!-- Dynamic list of constituencies -->
                    @php
                        $constituencies = $polling_stations->pluck('constituency')->unique()->filter()->sort();
                    @endphp
                    @foreach($constituencies as $constituency)
                        <option value="{{ $constituency }}">{{ $constituency }}</option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">All Statuses</option>
                    <option value="submitted">Submitted Results</option>
                    <option value="pending">Pending Results</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" id="resetFilters">
                    <i class="bi bi-x-circle me-1"></i> Reset
                </button>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row stats-row">
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="bi bi-building"></i>
                </div>
                <div class="stats-number">{{ $polling_stations->count() }}</div>
                <div class="stats-label">Total Stations</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="stats-number" id="submittedCount">
                    {{ $polling_stations->filter(function($station) { return $station->hasSubmittedVotes(); })->count() }}
                </div>
                <div class="stats-label">Submitted Results</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="bi bi-hourglass-split"></i>
                </div>
                <div class="stats-number" id="pendingCount">
                    {{ $polling_stations->filter(function($station) { return !$station->hasSubmittedVotes(); })->count() }}
                </div>
                <div class="stats-label">Pending Results</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="bi bi-people"></i>
                </div>
                <div class="stats-number">{{ $polling_stations->sum(function($station) { return $station->agents->count(); }) }}</div>
                <div class="stats-label">Total Agents</div>
            </div>
        </div>
    </div>
    
    <!-- Stations Grid View -->
    <div class="station-grid" id="stationsContainer">
        @foreach($polling_stations as $station)
        <div class="station-card" 
             data-name="{{ strtolower($station->name) }}" 
             data-constituency="{{ strtolower($station->constituency ?? '') }}" 
             data-status="{{ $station->hasSubmittedVotes() ? 'submitted' : 'pending' }}">
            
            <div class="status-badge {{ $station->hasSubmittedVotes() ? 'status-submitted' : 'status-pending' }}">
                @if($station->hasSubmittedVotes())
                    <i class="bi bi-check-circle me-1"></i> Submitted
                @else
                    <i class="bi bi-hourglass-split me-1"></i> Pending
                @endif
            </div>
            
            <div class="station-header">
                <h5 class="station-title">{{ $station->name }}</h5>
                <div class="station-subtitle">
                    @if($station->constituency)
                        <i class="bi bi-geo-alt me-1"></i> {{ $station->constituency }}
                    @else
                        <i class="bi bi-geo-alt me-1"></i> No constituency specified
                    @endif
                </div>
            </div>
            
            <div class="station-body">
                <div class="mb-3">
                    <h6 class="mb-2"><i class="bi bi-people me-1"></i> Agents ({{ $station->agents->count() }})</h6>
                    @if($station->agents->count() > 0)
                        <div class="agent-list">
                            @foreach($station->agents as $agent)
                                <div class="agent-item">
                                    <div class="agent-avatar">
                                        <i class="bi bi-person"></i>
                                    </div>
                                    <div>
                                        <div class="agent-name">{{ $agent->user->name }}</div>
                                        <div class="agent-phone">{{ $agent->user->phone_number }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-muted small">No agents assigned yet</p>
                    @endif
                </div>
                
                @if($station->hasSubmittedVotes())
                <div>
                    <h6 class="mb-2"><i class="bi bi-bar-chart me-1"></i> Votes Submitted</h6>
                    <div class="d-flex align-items-center">
                        <div class="me-2">
                            <span class="badge bg-success">{{ $station->submittedVotesCount() }}</span>
                        </div>
                        <div class="small text-muted">Total votes recorded</div>
                    </div>
                </div>
                @endif
            </div>
            
            <div class="station-footer">
                <a href="{{ route('polling_stations.show', $station->id) }}" class="btn btn-sm btn-light">
                    <i class="bi bi-people me-1"></i> Manage Agents
                </a>
                <div>
                    <a href="{{ route('polling_stations.edit', $station->id) }}" class="btn btn-sm btn-outline-warning">
                        <i class="bi bi-pencil"></i>
                    </a>
                    @if($station->agents->count() == 0)
                        <form action="{{ route('polling_stations.destroy', $station->id) }}" method="post" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this polling station?')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
        @endforeach
    </div>
    
    <!-- Empty State -->
    <div id="emptyState" class="text-center py-5" style="display: none;">
        <div class="mb-3">
            <i class="bi bi-search" style="font-size: 3rem; color: #dee2e6;"></i>
        </div>
        <h5>No polling stations found</h5>
        <p class="text-muted">Try adjusting your search or filters</p>
        <button id="clearFiltersBtn" class="btn btn-outline-secondary">
            <i class="bi bi-x-circle me-1"></i> Clear Filters
        </button>
    </div>
    
    <!-- Map View Toggle -->
    <div class="text-center mb-4">
        <button class="btn btn-outline-secondary" id="toggleMapView">
            <i class="bi bi-map me-1"></i> Toggle Map View
        </button>
    </div>
    
    <!-- Map View (Hidden by default) -->
    <div class="map-container" id="mapView" style="display: none;">
        <!-- Map will be initialized here with JavaScript -->
        <div id="stationsMap" style="height: 100%;"></div>
    </div>
</div>

<!-- Add Polling Station Modal (Reuse existing modal) -->
@include('polling_stations.partials.add_modal')

@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search and filtering functionality
        const searchInput = document.getElementById('searchInput');
        const constituencyFilter = document.getElementById('constituencyFilter');
        const statusFilter = document.getElementById('statusFilter');
        const resetFiltersBtn = document.getElementById('resetFilters');
        const clearFiltersBtn = document.getElementById('clearFiltersBtn');
        const stationsContainer = document.getElementById('stationsContainer');
        const emptyState = document.getElementById('emptyState');
        const stationCards = document.querySelectorAll('.station-card');
        const submittedCountEl = document.getElementById('submittedCount');
        const pendingCountEl = document.getElementById('pendingCount');
        const toggleMapViewBtn = document.getElementById('toggleMapView');
        const mapView = document.getElementById('mapView');
        
        // Filter function
        function filterStations() {
            const searchTerm = searchInput.value.toLowerCase();
            const constituency = constituencyFilter.value.toLowerCase();
            const status = statusFilter.value.toLowerCase();
            
            let visibleCount = 0;
            let submittedCount = 0;
            let pendingCount = 0;
            
            stationCards.forEach(card => {
                const name = card.dataset.name;
                const cardConstituency = card.dataset.constituency;
                const cardStatus = card.dataset.status;
                
                const matchesSearch = name.includes(searchTerm);
                const matchesConstituency = constituency === '' || cardConstituency.includes(constituency);
                const matchesStatus = status === '' || cardStatus === status;
                
                if (matchesSearch && matchesConstituency && matchesStatus) {
                    card.style.display = 'block';
                    visibleCount++;
                    
                    if (cardStatus === 'submitted') {
                        submittedCount++;
                    } else {
                        pendingCount++;
                    }
                } else {
                    card.style.display = 'none';
                }
            });
            
            // Update counters
            submittedCountEl.textContent = submittedCount;
            pendingCountEl.textContent = pendingCount;
            
            // Show/hide empty state
            if (visibleCount === 0) {
                emptyState.style.display = 'block';
                stationsContainer.classList.add('d-none');
            } else {
                emptyState.style.display = 'none';
                stationsContainer.classList.remove('d-none');
            }
        }
        
        // Event listeners
        searchInput.addEventListener('input', filterStations);
        constituencyFilter.addEventListener('change', filterStations);
        statusFilter.addEventListener('change', filterStations);
        
        resetFiltersBtn.addEventListener('click', function() {
            searchInput.value = '';
            constituencyFilter.value = '';
            statusFilter.value = '';
            filterStations();
        });
        
        clearFiltersBtn.addEventListener('click', function() {
            searchInput.value = '';
            constituencyFilter.value = '';
            statusFilter.value = '';
            filterStations();
        });
        
        // Toggle map view
        toggleMapViewBtn.addEventListener('click', function() {
            if (mapView.style.display === 'none') {
                mapView.style.display = 'block';
                initMap();
                this.innerHTML = '<i class="bi bi-grid me-1"></i> Show Grid View';
            } else {
                mapView.style.display = 'none';
                this.innerHTML = '<i class="bi bi-map me-1"></i> Toggle Map View';
            }
        });
        
        // Initialize map (placeholder function)
        function initMap() {
            // This would be implemented with a mapping library like Leaflet or Google Maps
            const mapElement = document.getElementById('stationsMap');
            
            // Placeholder message for demo purposes
            mapElement.innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100 bg-light">
                    <div class="text-center">
                        <i class="bi bi-map display-1 text-muted"></i>
                        <h4 class="mt-3">Map View</h4>
                        <p class="text-muted">This would display an interactive map with all polling stations.</p>
                        <p class="small">Implementation requires integrating a mapping library like Leaflet or Google Maps.</p>
                    </div>
                </div>
            `;
        }
    });
</script>
@endsection
