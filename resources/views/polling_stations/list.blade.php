@extends('layouts.app')

@section('content')
<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
    }
    
    .card-custom {
        border: none;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .card-header-custom {
        background: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.5rem;
    }
    
    .card-body-custom {
        padding: 1.5rem;
    }
    
    .btn-add {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border: none;
        color: #333;
        font-weight: 600;
        padding: 0.6rem 1.25rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 10px rgba(255, 215, 0, 0.3);
        transition: all 0.3s;
    }

    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(255, 215, 0, 0.4);
        color: #333;
        background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    }
    
    .btn-add i {
        margin-right: 8px;
        font-size: 1rem;
    }
    
    .table-custom {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
        margin-top: 0.5rem;
    }
    
    .table-custom thead th {
        border: none;
        background-color: #f8f9fa;
        color: #6c757d;
        font-weight: 500;
        padding: 0.75rem 1.25rem;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .table-custom tbody tr {
        background-color: #fff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
        border-radius: 8px;
        transition: all 0.2s;
    }
    
    .table-custom tbody tr:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.07);
    }
    
    .table-custom td {
        padding: 1rem 1.25rem;
        vertical-align: middle;
        border: none;
    }
    
    .table-custom td:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }
    
    .table-custom td:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
    }
    
    .station-name {
        font-weight: 500;
        color: #333;
    }
    
    .constituency-badge {
        background-color: rgba(255, 215, 0, 0.15);
        color: #FF8C00;
        font-size: 0.8rem;
        padding: 0.35rem 0.75rem;
        border-radius: 30px;
        font-weight: 500;
        display: inline-block;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
    }
    
    .btn-action {
        border: none;
        padding: 0.4rem 0.75rem;
        border-radius: 6px;
        font-weight: 500;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        transition: all 0.2s;
    }
    
    .btn-action i {
        margin-right: 5px;
        font-size: 0.9rem;
    }
    
    .btn-agents {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }
    
    .btn-agents:hover {
        background-color: #198754;
        color: white;
    }
    
    .btn-edit {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .btn-edit:hover {
        background-color: #0d6efd;
        color: white;
    }
    
    .btn-delete {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    .btn-delete:hover {
        background-color: #dc3545;
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
    }
    
    .empty-state-icon {
        font-size: 3rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }
    
    .empty-state-text {
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 1.5rem;
    }
    


    .location-info {
        line-height: 1.3;
    }

    .location-info small {
        font-size: 0.8rem;
    }

    /* Stats Cards */
    .stats-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .stats-icon {
        width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .stats-icon.bg-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .stats-icon.bg-success {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    }

    .stats-icon.bg-warning {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%) !important;
    }

    .stats-icon.bg-info {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
    }

    /* Filter section styling */
    .form-select-sm:focus {
        border-color: #FFD700;
        box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
    }

    /* Card View Styling */
    .station-card-item {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
    }

    .station-card-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }

    .station-card-item .card-title {
        color: #333;
        font-size: 1rem;
    }

    .station-card-item .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        border-radius: 6px;
    }

    /* View toggle buttons */
    .btn-group .btn.active {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border-color: #FFD700;
        color: #333;
    }
</style>

<div class="container">
    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h4 class="page-title">Polling Stations</h4>
            <p class="text-muted">Manage all polling stations in the election</p>
        </div>
        <div class="d-flex gap-2">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary btn-sm" id="tableView">
                    <i class="bi bi-table"></i> Table
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" id="cardView">
                    <i class="bi bi-grid-3x3-gap"></i> Cards
                </button>
            </div>
            <button type="button" class="btn btn-outline-primary view-toggle" id="toggleMapView">
                <i class="bi bi-map"></i> Map
            </button>
            <a href="{{ route('polling_stations.create') }}" class="btn btn-add">
                <i class="bi bi-plus-circle"></i> Add Polling Station
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary">
                            <i class="bi bi-building text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold">{{ $polling_stations->count() }}</h5>
                            <small class="text-muted">Total Stations</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success">
                            <i class="bi bi-geo-alt text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold" id="stationsWithCoordsCard">{{ $polling_stations->whereNotNull('latitude')->whereNotNull('longitude')->count() }}</h5>
                            <small class="text-muted">With Coordinates</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning">
                            <i class="bi bi-people text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold">{{ $polling_stations->sum(function($station) { return $station->agents->count(); }) }}</h5>
                            <small class="text-muted">Total Agents</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info">
                            <i class="bi bi-geo text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold">{{ $polling_stations->whereNotNull('district')->groupBy('district')->count() }}</h5>
                            <small class="text-muted">Districts</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Filters Section -->
    <div class="card card-custom mb-4">
        <div class="card-header-custom">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="bi bi-funnel me-2 text-warning"></i>
                    <span class="fw-medium">Advanced Filters</span>
                    <span class="badge bg-info ms-2">{{ $polling_stations->total() }} stations found</span>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-primary" id="toggleMapView">
                        <i class="bi bi-map"></i> Map View
                    </button>
                    <a href="{{ route('polling_stations.index') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-x-circle me-1"></i>Clear All
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <form method="GET" action="{{ route('polling_stations.index') }}" id="filtersForm">
                <div class="row g-3">
                    <!-- Search -->
                    <div class="col-md-3">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-search me-1"></i>Search
                        </label>
                        <input type="text" name="search" class="form-control form-control-sm"
                               placeholder="Search stations, locations..."
                               value="{{ $currentFilters['search'] ?? '' }}">
                    </div>

                    <!-- District Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-geo-alt me-1"></i>District
                        </label>
                        <select name="district" class="form-select form-select-sm" id="districtFilter">
                            <option value="">All Districts</option>
                            @foreach($districts as $district)
                                <option value="{{ $district }}" {{ ($currentFilters['district'] ?? '') == $district ? 'selected' : '' }}>
                                    {{ $district }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- County Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-map me-1"></i>County
                        </label>
                        <select name="county" class="form-select form-select-sm">
                            <option value="">All Counties</option>
                            @foreach($counties as $county)
                                <option value="{{ $county }}" {{ ($currentFilters['county'] ?? '') == $county ? 'selected' : '' }}>
                                    {{ $county }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Subcounty Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-building me-1"></i>Subcounty
                        </label>
                        <select name="subcounty" class="form-select form-select-sm">
                            <option value="">All Subcounties</option>
                            @foreach($subcounties as $subcounty)
                                <option value="{{ $subcounty }}" {{ ($currentFilters['subcounty'] ?? '') == $subcounty ? 'selected' : '' }}>
                                    {{ $subcounty }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Coordinates Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-geo me-1"></i>Coordinates
                        </label>
                        <select name="coordinates" class="form-select form-select-sm">
                            <option value="">All Stations</option>
                            <option value="with" {{ ($currentFilters['coordinates'] ?? '') == 'with' ? 'selected' : '' }}>With Coordinates</option>
                            <option value="without" {{ ($currentFilters['coordinates'] ?? '') == 'without' ? 'selected' : '' }}>Without Coordinates</option>
                        </select>
                    </div>

                    <!-- Agents Filter -->
                    <div class="col-md-1">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-people me-1"></i>Agents
                        </label>
                        <select name="agents" class="form-select form-select-sm">
                            <option value="">All</option>
                            <option value="with" {{ ($currentFilters['agents'] ?? '') == 'with' ? 'selected' : '' }}>With</option>
                            <option value="without" {{ ($currentFilters['agents'] ?? '') == 'without' ? 'selected' : '' }}>Without</option>
                        </select>
                    </div>
                </div>

                <div class="row g-3 mt-2">
                    <!-- Results Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-check-circle me-1"></i>Results
                        </label>
                        <select name="results" class="form-select form-select-sm">
                            <option value="">All Stations</option>
                            <option value="submitted" {{ ($currentFilters['results'] ?? '') == 'submitted' ? 'selected' : '' }}>Submitted</option>
                            <option value="pending" {{ ($currentFilters['results'] ?? '') == 'pending' ? 'selected' : '' }}>Pending</option>
                        </select>
                    </div>

                    <!-- Evidence Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-file-earmark me-1"></i>Evidence
                        </label>
                        <select name="evidence" class="form-select form-select-sm">
                            <option value="">All Stations</option>
                            <option value="with" {{ ($currentFilters['evidence'] ?? '') == 'with' ? 'selected' : '' }}>With Evidence</option>
                            <option value="without" {{ ($currentFilters['evidence'] ?? '') == 'without' ? 'selected' : '' }}>Without Evidence</option>
                        </select>
                    </div>

                    <!-- Sort By -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-sort-down me-1"></i>Sort By
                        </label>
                        <select name="sort_by" class="form-select form-select-sm">
                            <option value="name" {{ ($currentFilters['sort_by'] ?? 'name') == 'name' ? 'selected' : '' }}>Name</option>
                            <option value="district" {{ ($currentFilters['sort_by'] ?? '') == 'district' ? 'selected' : '' }}>District</option>
                            <option value="county" {{ ($currentFilters['sort_by'] ?? '') == 'county' ? 'selected' : '' }}>County</option>
                            <option value="created_at" {{ ($currentFilters['sort_by'] ?? '') == 'created_at' ? 'selected' : '' }}>Date Created</option>
                        </select>
                    </div>

                    <!-- Sort Order -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-arrow-up-down me-1"></i>Order
                        </label>
                        <select name="sort_order" class="form-select form-select-sm">
                            <option value="asc" {{ ($currentFilters['sort_order'] ?? 'asc') == 'asc' ? 'selected' : '' }}>Ascending</option>
                            <option value="desc" {{ ($currentFilters['sort_order'] ?? '') == 'desc' ? 'selected' : '' }}>Descending</option>
                        </select>
                    </div>

                    <!-- Per Page -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-list me-1"></i>Per Page
                        </label>
                        <select name="per_page" class="form-select form-select-sm">
                            <option value="10" {{ ($currentFilters['per_page'] ?? 20) == 10 ? 'selected' : '' }}>10</option>
                            <option value="20" {{ ($currentFilters['per_page'] ?? 20) == 20 ? 'selected' : '' }}>20</option>
                            <option value="50" {{ ($currentFilters['per_page'] ?? 20) == 50 ? 'selected' : '' }}>50</option>
                            <option value="100" {{ ($currentFilters['per_page'] ?? 20) == 100 ? 'selected' : '' }}>100</option>
                        </select>
                    </div>

                    <!-- Action Buttons -->
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary btn-sm w-100">
                            <i class="bi bi-funnel me-1"></i>Apply Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Enhanced Map View Container -->
    <div class="card card-custom mb-4" id="mapViewContainer" style="display: none;">
        <div class="card-header-custom">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex align-items-center">
                    <i class="bi bi-map me-2 text-primary"></i>
                    <span class="fw-medium">Polling Stations Map</span>
                    <span class="badge bg-info ms-2" id="mapStationsCount">{{ $stationsWithCoordinates }} stations</span>
                </div>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="closeMapView">
                    <i class="bi bi-x-lg"></i> Close Map
                </button>
            </div>

            <!-- Map Controls -->
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label small fw-bold">
                        <i class="bi bi-zoom-in me-1"></i>Zoom to District
                    </label>
                    <select class="form-select form-select-sm" id="districtZoomSelector">
                        <option value="">All Districts</option>
                        @foreach($districts as $district)
                            <option value="{{ $district }}">{{ $district }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small fw-bold">
                        <i class="bi bi-layers me-1"></i>Clustering
                    </label>
                    <select class="form-select form-select-sm" id="clusteringSelector">
                        <option value="enabled">Enabled</option>
                        <option value="disabled">Disabled</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small fw-bold">
                        <i class="bi bi-eye me-1"></i>Show Only
                    </label>
                    <select class="form-select form-select-sm" id="mapFilterSelector">
                        <option value="all">All Stations</option>
                        <option value="with-coordinates">With Coordinates</option>
                        <option value="with-agents">With Agents</option>
                        <option value="with-results">With Results</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="button" class="btn btn-primary btn-sm w-100" id="refreshMapBtn">
                        <i class="bi bi-arrow-clockwise me-1"></i>Refresh Map
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body-custom p-0" style="position: relative;">
            <div id="stationsMap" style="height: 600px; width: 100%;"></div>
            <div id="mapLoading" class="map-loading" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="mt-2">Loading map...</div>
            </div>

            <!-- Map Legend -->
            <div class="map-legend position-absolute bottom-0 start-0 m-3 bg-white rounded shadow-sm p-2" style="z-index: 1000;">
                <div class="small">
                    <div class="d-flex align-items-center mb-1">
                        <div class="legend-marker bg-success rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                        <span>With Coordinates</span>
                    </div>
                    <div class="d-flex align-items-center mb-1">
                        <div class="legend-marker bg-warning rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                        <span>With Agents</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="legend-marker bg-primary rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                        <span>With Results</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content Card -->
    <div class="card card-custom" id="listViewContainer">
        <div class="card-header-custom d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <i class="bi bi-building me-2 text-warning"></i>
                <span class="fw-medium">All Polling Stations</span>
            </div>
            <div class="input-group" style="width: 300px;">
                <input type="text" class="form-control" placeholder="Search by name or location..." id="searchInput">
                <span class="input-group-text bg-light">
                    <i class="bi bi-search"></i>
                </span>
            </div>
        </div>
        <div class="card-body-custom p-0">
            @if(count($polling_stations) > 0)
            <div class="table-responsive">
                <table class="table table-custom">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Location</th>
                            <th>Coordinates</th>
                            <th>Agents</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($polling_stations as $polling_station)
                            <tr data-station-id="{{ $polling_station->id }}">
                                <td>
                                    <span class="station-name">{{ $polling_station->name }}</span>
                                </td>
                                <td>
                                    @if($polling_station->district || $polling_station->county || $polling_station->subcounty)
                                        <div class="location-info">
                                            @if($polling_station->district)
                                                <div class="text-primary fw-bold">{{ $polling_station->district }} District</div>
                                            @endif
                                            @if($polling_station->county)
                                                <small class="text-muted">{{ $polling_station->county }}</small>
                                            @endif
                                            @if($polling_station->subcounty)
                                                <small class="text-muted"> • {{ $polling_station->subcounty }}</small>
                                            @endif
                                            @if($polling_station->parish)
                                                <small class="text-muted"> • {{ $polling_station->parish }}</small>
                                            @endif
                                            @if($polling_station->village)
                                                <div><small class="badge bg-light text-dark">{{ $polling_station->village }}</small></div>
                                            @endif
                                        </div>
                                    @elseif($polling_station->constituency)
                                        <span class="constituency-badge">{{ $polling_station->constituency }}</span>
                                    @else
                                        <span class="text-muted">Location not specified</span>
                                    @endif
                                </td>
                                <td>
                                    @if($polling_station->latitude && $polling_station->longitude)
                                        <span class="badge bg-success coordinates-badge" 
                                              data-lat="{{ $polling_station->latitude }}" 
                                              data-lng="{{ $polling_station->longitude }}" 
                                              data-name="{{ $polling_station->name }}">
                                            <i class="bi bi-geo-alt me-1"></i>
                                            {{ number_format($polling_station->latitude, 4) }}, {{ number_format($polling_station->longitude, 4) }}
                                        </span>
                                    @else
                                        <span class="badge bg-light text-muted">
                                            <i class="bi bi-geo-alt-fill me-1"></i>
                                            Not set
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        {{ $polling_station->agents->count() }} agents
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons justify-content-end">
                                        <a href="{{ route('polling_stations.show',$polling_station->id) }}" class="btn btn-action btn-agents">
                                            <i class="bi bi-people"></i> Agents
                                        </a>
                                        <a href="{{ route('polling_stations.edit',$polling_station->id) }}" class="btn btn-action btn-edit">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a>
                                        @if($polling_station->agents->count() == 0)
                                            <form action="{{ route('polling_stations.destroy',$polling_station->id) }}" method="post" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-action btn-delete" onclick="return confirm('Are you sure you want to delete this polling station?')">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>                            
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Card View (Hidden by default) -->
            <div id="cardViewContainer" style="display: none;">
                @if(count($polling_stations) > 0)
                <div class="row g-3">
                    @foreach ($polling_stations as $polling_station)
                    <div class="col-md-6 col-lg-4 station-card"
                         data-name="{{ strtolower($polling_station->name) }}"
                         data-district="{{ $polling_station->district ?? '' }}"
                         data-county="{{ $polling_station->county ?? '' }}"
                         data-has-coordinates="{{ ($polling_station->latitude && $polling_station->longitude) ? 'true' : 'false' }}"
                         data-agents-count="{{ $polling_station->agents->count() }}">
                        <div class="card station-card-item border-0 shadow-sm h-100">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0 fw-bold">{{ $polling_station->name }}</h6>
                                    @if($polling_station->latitude && $polling_station->longitude)
                                        <span class="badge bg-success">
                                            <i class="bi bi-geo-alt"></i>
                                        </span>
                                    @else
                                        <span class="badge bg-light text-muted">
                                            <i class="bi bi-geo-alt"></i>
                                        </span>
                                    @endif
                                </div>

                                <!-- Location Info -->
                                @if($polling_station->district || $polling_station->county || $polling_station->subcounty)
                                <div class="location-info mb-2">
                                    @if($polling_station->district)
                                        <div class="text-primary fw-bold small">{{ $polling_station->district }} District</div>
                                    @endif
                                    @if($polling_station->county)
                                        <small class="text-muted">{{ $polling_station->county }}</small>
                                    @endif
                                    @if($polling_station->subcounty)
                                        <small class="text-muted"> • {{ $polling_station->subcounty }}</small>
                                    @endif
                                    @if($polling_station->village)
                                        <div><small class="badge bg-light text-dark mt-1">{{ $polling_station->village }}</small></div>
                                    @endif
                                </div>
                                @elseif($polling_station->constituency)
                                <div class="mb-2">
                                    <span class="constituency-badge">{{ $polling_station->constituency }}</span>
                                </div>
                                @endif

                                <!-- Stats -->
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <small class="text-muted">
                                        <i class="bi bi-people me-1"></i>{{ $polling_station->agents->count() }} agents
                                    </small>
                                    @if($polling_station->latitude && $polling_station->longitude)
                                        <small class="text-success">
                                            <i class="bi bi-geo-alt me-1"></i>Mapped
                                        </small>
                                    @else
                                        <small class="text-muted">
                                            <i class="bi bi-geo-alt me-1"></i>No coordinates
                                        </small>
                                    @endif
                                </div>

                                <!-- Actions -->
                                <div class="d-flex gap-1">
                                    <a href="{{ route('polling_stations.show',$polling_station->id) }}" class="btn btn-sm btn-outline-success flex-fill">
                                        <i class="bi bi-people"></i> Agents
                                    </a>
                                    <a href="{{ route('polling_stations.edit',$polling_station->id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    @if($polling_station->agents->count() == 0)
                                        <form action="{{ route('polling_stations.destroy',$polling_station->id) }}" method="post" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure?')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="bi bi-building"></i>
                    </div>
                    <h5 class="empty-state-text">No polling stations found</h5>
                    <a href="{{ route('polling_stations.create') }}" class="btn btn-add">
                        <i class="bi bi-plus-circle"></i> Add Your First Polling Station
                    </a>
                </div>
                @endif
            </div>

            @else
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="bi bi-building"></i>
                </div>
                <h5 class="empty-state-text">No polling stations found</h5>
                <a href="{{ route('polling_stations.create') }}" class="btn btn-add">
                    <i class="bi bi-plus-circle"></i> Add Your First Polling Station
                </a>
            </div>
            @endif

            <!-- Pagination Controls -->
            @if($polling_stations->hasPages())
            <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                <div class="showing-entries">
                    <small class="text-muted">
                        Showing {{ $polling_stations->firstItem() ?? 0 }} to {{ $polling_stations->lastItem() ?? 0 }}
                        of {{ $polling_stations->total() }} stations
                    </small>
                </div>
                <div class="pagination-container">
                    {{ $polling_stations->links('pagination::bootstrap-4') }}
                </div>
            </div>
            @endif
        </div>
    </div>
</div>



<!-- Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""/>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>

<!-- Leaflet MarkerCluster Plugin -->
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
<script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>

<style>
    .coordinates-badge {
        cursor: pointer;
        transition: all 0.2s;
    }
    .coordinates-badge:hover {
        background-color: #198754 !important;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        transform: scale(1.05);
    }

    /* Map container styling */
    #stationsMap {
        border-radius: 8px;
        overflow: hidden;
    }

    /* Map popup styling */
    .leaflet-popup-content-wrapper {
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .map-popup {
        text-align: center;
        padding: 5px;
    }

    .map-popup strong {
        color: #333;
        font-size: 14px;
    }

    .map-popup .text-muted {
        color: #666 !important;
        font-size: 12px;
    }

    /* Map controls styling */
    .leaflet-control-zoom a {
        background-color: #fff;
        border: 1px solid #ccc;
        color: #333;
    }

    .leaflet-control-zoom a:hover {
        background-color: #f8f9fa;
        border-color: #FFD700;
    }

    /* Map loading indicator */
    .map-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 8px;
        text-align: center;
    }

    /* Custom marker cluster styles */
    .marker-cluster-small {
        background-color: rgba(255, 215, 0, 0.6);
        border: 2px solid rgba(255, 215, 0, 0.8);
    }

    .marker-cluster-small div {
        background-color: rgba(255, 215, 0, 0.8);
        color: #333;
        font-weight: bold;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .marker-cluster-medium {
        background-color: rgba(255, 165, 0, 0.6);
        border: 2px solid rgba(255, 165, 0, 0.8);
    }

    .marker-cluster-medium div {
        background-color: rgba(255, 165, 0, 0.8);
        color: white;
        font-weight: bold;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .marker-cluster-large {
        background-color: rgba(255, 69, 0, 0.6);
        border: 2px solid rgba(255, 69, 0, 0.8);
    }

    .marker-cluster-large div {
        background-color: rgba(255, 69, 0, 0.8);
        color: white;
        font-weight: bold;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Custom marker styles */
    .custom-marker {
        background: transparent;
        border: none;
    }

    /* Map legend styles */
    .map-legend {
        background: rgba(255, 255, 255, 0.95) !important;
        border: 1px solid #ddd;
        font-size: 0.85rem;
    }

    .legend-marker {
        flex-shrink: 0;
    }

    /* Loading overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: none;
        z-index: 9999;
        align-items: center;
        justify-content: center;
    }

    .loading-overlay.show {
        display: flex;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // View switching functionality
        const tableViewBtn = document.getElementById('tableView');
        const cardViewBtn = document.getElementById('cardView');
        const tableContainer = document.querySelector('.table-responsive');
        const cardContainer = document.getElementById('cardViewContainer');

        // Set initial view (table)
        tableViewBtn.classList.add('active');

        tableViewBtn.addEventListener('click', function() {
            tableContainer.style.display = 'block';
            cardContainer.style.display = 'none';
            tableViewBtn.classList.add('active');
            cardViewBtn.classList.remove('active');
        });

        cardViewBtn.addEventListener('click', function() {
            tableContainer.style.display = 'none';
            cardContainer.style.display = 'block';
            cardViewBtn.classList.add('active');
            tableViewBtn.classList.remove('active');
        });

        // Enhanced server-side filtering functionality
        const filtersForm = document.getElementById('filtersForm');
        const searchInput = document.querySelector('input[name="search"]');
        const districtFilter = document.getElementById('districtFilter');

        // Auto-submit form when filters change (with debouncing)
        let filterTimeout;

        function debounceFilter(callback, delay = 500) {
            clearTimeout(filterTimeout);
            filterTimeout = setTimeout(callback, delay);
        }

        // Auto-submit on filter change
        if (filtersForm) {
            const filterInputs = filtersForm.querySelectorAll('select, input[name="search"]');

            filterInputs.forEach(input => {
                if (input.name === 'search') {
                    // Debounce search input
                    input.addEventListener('input', function() {
                        debounceFilter(() => {
                            showLoadingOverlay();
                            filtersForm.submit();
                        });
                    });
                } else {
                    // Immediate submit for select filters
                    input.addEventListener('change', function() {
                        showLoadingOverlay();
                        filtersForm.submit();
                    });
                }
            });
        }

        // Loading overlay functions
        function showLoadingOverlay() {
            let overlay = document.querySelector('.loading-overlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.className = 'loading-overlay';
                overlay.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="mt-2">Applying filters...</div>
                    </div>
                `;
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.8);
                    display: flex;
                    z-index: 9999;
                    align-items: center;
                    justify-content: center;
                `;
                document.body.appendChild(overlay);
            }
            overlay.style.display = 'flex';
        }

        function hideLoadingOverlay() {
            const overlay = document.querySelector('.loading-overlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // Hide loading overlay when page loads
        hideLoadingOverlay();

        // Performance optimization: Lazy load images
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
        // Enhanced Map functionality with clustering and district zoom
        let map;
        let markers = [];
        let markerClusterGroup;

        // Wait for DOM to be fully loaded before getting coordinate badges
        const getCoordinatesBadges = () => document.querySelectorAll('.coordinates-badge');
        let coordinatesBadges = getCoordinatesBadges();

        // Map controls
        const toggleMapViewBtn = document.getElementById('toggleMapView');
        const closeMapViewBtn = document.getElementById('closeMapView');
        const mapViewContainer = document.getElementById('mapViewContainer');
        const listViewContainer = document.getElementById('listViewContainer');
        const districtZoomSelector = document.getElementById('districtZoomSelector');
        const clusteringSelector = document.getElementById('clusteringSelector');
        const mapFilterSelector = document.getElementById('mapFilterSelector');
        const refreshMapBtn = document.getElementById('refreshMapBtn');

        // District boundaries for Uganda (approximate coordinates)
        const districtBounds = {
            'Oyam': {
                center: [2.3, 32.6],
                bounds: [[2.0, 32.2], [2.6, 33.0]],
                zoom: 11
            },
            'Kampala': {
                center: [0.3476, 32.5825],
                bounds: [[0.2, 32.4], [0.5, 32.8]],
                zoom: 12
            },
            'Wakiso': {
                center: [0.4043, 32.4597],
                bounds: [[0.2, 32.2], [0.6, 32.7]],
                zoom: 11
            },
            'Mukono': {
                center: [0.3533, 32.7556],
                bounds: [[0.1, 32.5], [0.6, 33.0]],
                zoom: 11
            },
            'Jinja': {
                center: [0.4244, 33.2042],
                bounds: [[0.3, 33.0], [0.6, 33.4]],
                zoom: 12
            },
            'Mbale': {
                center: [1.0827, 34.1753],
                bounds: [[0.9, 34.0], [1.3, 34.4]],
                zoom: 11
            },
            'Gulu': {
                center: [2.7796, 32.2983],
                bounds: [[2.5, 32.0], [3.0, 32.6]],
                zoom: 11
            },
            'Lira': {
                center: [2.2490, 32.8998],
                bounds: [[2.0, 32.6], [2.5, 33.2]],
                zoom: 11
            },
            'Arua': {
                center: [3.0197, 30.9108],
                bounds: [[2.8, 30.6], [3.3, 31.2]],
                zoom: 11
            },
            'Mbarara': {
                center: [-0.6103, 30.6588],
                bounds: [[-0.8, 30.4], [-0.4, 30.9]],
                zoom: 11
            }
        };
                if (selectedAgents === 'with' && station.agentsCount === 0) {
                    visible = false;
                } else if (selectedAgents === 'without' && station.agentsCount > 0) {
                    visible = false;
                }

                // Apply visibility to both table row and card
                if (station.tableRow) {
                    station.tableRow.style.display = visible ? '' : 'none';
                }
                if (station.cardElement) {
                    station.cardElement.style.display = visible ? '' : 'none';
                }

                if (visible) visibleCount++;
            });

            // Update visible count
            updateVisibleCount(visibleCount);
        }

        // Update county filter based on district selection
        function updateCountyFilter(selectedDistrict) {
            countyFilter.innerHTML = '<option value="">All Counties</option>';

            if (selectedDistrict) {
                const counties = new Set();
                stationsData.forEach(station => {
                    if (station.district === selectedDistrict && station.county) {
                        counties.add(station.county);
                    }
                });

                counties.forEach(county => {
                    const option = document.createElement('option');
                    option.value = county;
                    option.textContent = county;
                    countyFilter.appendChild(option);
                });

                countyFilter.disabled = counties.size === 0;
            } else {
                countyFilter.disabled = true;
            }
        }

        // Update visible count display
        function updateVisibleCount(count) {
            const totalCount = stationsData.length;
            const countDisplay = document.querySelector('.card-header-custom .fw-medium');
            if (countDisplay) {
                countDisplay.innerHTML = `All Polling Stations <span class="badge bg-info ms-2">${count} of ${totalCount} shown</span>`;
            }
        }

        // Event listeners with error handling
        if (searchInput) {
            searchInput.addEventListener('keyup', applyFilters);
        } else {
            console.error('Search input not found');
        }

        if (districtFilter) {
            districtFilter.addEventListener('change', function() {
                updateCountyFilter(this.value);
                if (countyFilter) countyFilter.value = ''; // Reset county selection
                applyFilters();
            });
        }

        if (countyFilter) {
            countyFilter.addEventListener('change', applyFilters);
        }

        if (coordinatesFilter) {
            coordinatesFilter.addEventListener('change', applyFilters);
        }

        if (agentsFilter) {
            agentsFilter.addEventListener('change', applyFilters);
        }

        // Clear filters
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', function() {
                if (searchInput) searchInput.value = '';
                if (districtFilter) districtFilter.value = '';
                if (countyFilter) countyFilter.value = '';
                if (coordinatesFilter) coordinatesFilter.value = '';
                if (agentsFilter) agentsFilter.value = '';
                updateCountyFilter('');
                applyFilters();
            });
        }

        // Initialize
        console.log('Stations data loaded:', stationsData.length);
        updateVisibleCount(stationsData.length);

        // Debug: Log first few stations data
        if (stationsData.length > 0) {
            console.log('Sample station data:', stationsData.slice(0, 3));
        }
        


        // Function to check if coordinates are within Uganda
        function isWithinUganda(lat, lng) {
            // Uganda's approximate bounding box
            const ugandaBounds = {
                minLat: -1.5, // Southern border
                maxLat: 4.3,  // Northern border
                minLng: 29.5, // Western border
                maxLng: 35.0  // Eastern border
            };

            return lat >= ugandaBounds.minLat &&
                   lat <= ugandaBounds.maxLat &&
                   lng >= ugandaBounds.minLng &&
                   lng <= ugandaBounds.maxLng;
        }
        
        // Map functionality
        let map;
        let markers = [];
        const coordinatesBadges = document.querySelectorAll('.coordinates-badge');
        const stationsWithCoordinatesCount = coordinatesBadges.length;

        // Update coordinates count if element exists
        const coordsCountElement = document.getElementById('stationsWithCoordinates');
        if (coordsCountElement) {
            coordsCountElement.textContent = stationsWithCoordinatesCount + ' with coordinates';
        }

        // Toggle between list and map views
        const toggleMapViewBtn = document.getElementById('toggleMapView');
        const closeMapViewBtn = document.getElementById('closeMapView');
        const mapViewContainer = document.getElementById('mapViewContainer');
        const listViewContainer = document.getElementById('listViewContainer');

        function initMap() {
            try {
                // Show loading indicator
                const loadingIndicator = document.getElementById('mapLoading');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'block';
                }

                // Check if Leaflet is loaded
                if (typeof L === 'undefined') {
                    console.error('Leaflet library not loaded');
                    if (loadingIndicator) {
                        loadingIndicator.innerHTML = '<div class="text-danger">Error: Map library not loaded</div>';
                    }
                    return;
                }

                if (!map) {
                    // Clear any existing map container
                    const mapContainer = document.getElementById('stationsMap');
                    if (!mapContainer) {
                        console.error('Map container not found');
                        if (loadingIndicator) {
                            loadingIndicator.innerHTML = '<div class="text-danger">Error: Map container not found</div>';
                        }
                        return;
                    }

                    // Initialize map
                    map = L.map('stationsMap', {
                        zoomControl: true,
                        scrollWheelZoom: true,
                        preferCanvas: true
                    });

                    // Add the OpenStreetMap tiles
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        maxZoom: 19,
                        attribution: '&copy; <a href="https://openstreetmap.org/copyright">OpenStreetMap contributors</a>'
                    }).addTo(map);

                    // Set initial view to Oyam District (default focus)
                    const oyamBounds = districtBounds['Oyam'];
                    if (oyamBounds) {
                        map.setView(oyamBounds.center, oyamBounds.zoom);
                    } else {
                        // Fallback to Uganda view
                        map.setView([1.3733, 32.2903], 7);
                    }

                    // Initialize marker cluster group
                    markerClusterGroup = L.markerClusterGroup({
                        chunkedLoading: true,
                        maxClusterRadius: 50,
                        spiderfyOnMaxZoom: true,
                        showCoverageOnHover: false,
                        zoomToBoundsOnClick: true,
                        iconCreateFunction: function(cluster) {
                            const count = cluster.getChildCount();
                            let className = 'marker-cluster-small';
                            if (count > 10) className = 'marker-cluster-medium';
                            if (count > 50) className = 'marker-cluster-large';

                            return L.divIcon({
                                html: '<div><span>' + count + '</span></div>',
                                className: 'marker-cluster ' + className,
                                iconSize: L.point(40, 40)
                            });
                        }
                    });

                    map.addLayer(markerClusterGroup);
                }

                // Clear existing markers
                markers = [];
                if (markerClusterGroup) {
                    markerClusterGroup.clearLayers();
                }

                // Add markers for all stations with coordinates
                const bounds = L.latLngBounds();
                let hasValidMarkers = false;

                    // Get station data from the current page
                    const stationRows = document.querySelectorAll('tbody tr[data-station-id]');

                    stationRows.forEach(row => {
                        const coordinatesBadge = row.querySelector('.coordinates-badge');
                        if (!coordinatesBadge) return;

                        const lat = parseFloat(coordinatesBadge.dataset.lat);
                        const lng = parseFloat(coordinatesBadge.dataset.lng);
                        const name = coordinatesBadge.dataset.name;
                        const stationId = row.dataset.stationId;

                        if (!isNaN(lat) && !isNaN(lng) && isWithinUganda(lat, lng)) {
                            try {
                                // Determine marker color based on station status
                                const agentsBadge = row.querySelector('td:nth-child(4) .badge');
                                const hasAgents = agentsBadge && !agentsBadge.textContent.includes('0 agents');

                                // For this table, we don't have results column, so we'll use agents status
                                const hasResults = false; // No results column in this table

                                let markerColor = '#28a745'; // Green for stations with coordinates
                                let statusText = 'Has coordinates';

                                if (hasResults) {
                                    markerColor = '#007bff'; // Blue for stations with results
                                    statusText = 'Has results submitted';
                                } else if (hasAgents) {
                                    markerColor = '#ffc107'; // Yellow for stations with agents
                                    statusText = 'Has agents assigned';
                                }

                                // Create custom icon
                                const customIcon = L.divIcon({
                                    className: 'custom-marker',
                                    html: `<div style="background-color: ${markerColor}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
                                    iconSize: [20, 20],
                                    iconAnchor: [10, 10]
                                });

                                const marker = L.marker([lat, lng], { icon: customIcon });

                                // Enhanced popup with station details
                                const locationInfo = row.querySelector('.location-info');
                                const district = locationInfo?.querySelector('.text-primary')?.textContent || 'Unknown';
                                const agentsText = agentsBadge?.textContent || '0 agents';

                                marker.bindPopup(`
                                    <div class="map-popup">
                                        <h6 class="mb-2">${name}</h6>
                                        <div class="mb-1"><strong>District:</strong> ${district}</div>
                                        <div class="mb-1"><strong>Agents:</strong> ${agentsText}</div>
                                        <div class="mb-1"><strong>Status:</strong> <span style="color: ${markerColor};">${statusText}</span></div>
                                        <div class="mb-2"><small class="text-muted">Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}</small></div>
                                        <div class="d-flex gap-1">
                                            <a href="/polling_stations/${stationId}" class="btn btn-sm btn-outline-primary">View Details</a>
                                        </div>
                                    </div>
                                `);

                                // Add to cluster group or directly to map based on clustering setting
                                const clusteringEnabled = clusteringSelector?.value !== 'disabled';
                                if (clusteringEnabled && markerClusterGroup) {
                                    markerClusterGroup.addLayer(marker);
                                } else {
                                    marker.addTo(map);
                                }

                                markers.push(marker);
                                bounds.extend([lat, lng]);
                                hasValidMarkers = true;
                            } catch (error) {
                                console.error('Error adding marker:', error);
                            }
                        }
                    });

                    // Set map view based on selected district or show all markers
                    const selectedDistrict = districtZoomSelector?.value;
                    if (selectedDistrict && districtBounds[selectedDistrict]) {
                        const districtData = districtBounds[selectedDistrict];
                        map.setView(districtData.center, districtData.zoom);
                    } else if (hasValidMarkers) {
                        map.fitBounds(bounds, { padding: [20, 20] });
                    } else {
                        // Default view centered on Oyam
                        const oyamBounds = districtBounds['Oyam'];
                        if (oyamBounds) {
                            map.setView(oyamBounds.center, oyamBounds.zoom);
                        } else {
                            map.setView([1.3733, 32.2903], 7);
                        }
                    }

                    console.log(`Map initialized with ${markers.length} markers`);
                    console.log('Station rows found:', stationRows.length);
                    console.log('Coordinate badges found:', document.querySelectorAll('.coordinates-badge').length);

                    // Update map stations count
                    const mapStationsCount = document.getElementById('mapStationsCount');
                    if (mapStationsCount) {
                        mapStationsCount.textContent = `${markers.length} stations`;
                    }
                }

                // Force map resize after a short delay
                setTimeout(() => {
                    if (map) {
                        map.invalidateSize();

                        // Hide loading indicator
                        const loadingIndicator = document.getElementById('mapLoading');
                        if (loadingIndicator) {
                            loadingIndicator.style.display = 'none';
                        }
                    }
                }, 200);

            } catch (error) {
                console.error('Error initializing map:', error);

                // Hide loading indicator and show error
                const loadingIndicator = document.getElementById('mapLoading');
                if (loadingIndicator) {
                    loadingIndicator.innerHTML = '<div class="text-danger">Error loading map</div>';
                    setTimeout(() => {
                        loadingIndicator.style.display = 'none';
                    }, 3000);
                }
            }
        }
        
        // Map toggle functionality with error handling
        if (toggleMapViewBtn && mapViewContainer && listViewContainer) {
            toggleMapViewBtn.addEventListener('click', function() {
                try {
                    mapViewContainer.style.display = 'block';
                    listViewContainer.style.display = 'none';

                    // Update button state
                    this.classList.add('btn-primary');
                    this.classList.remove('btn-outline-primary');
                    this.innerHTML = '<i class="bi bi-map-fill"></i> Map';

                    // Initialize map after a short delay to ensure container is visible
                    setTimeout(() => {
                        initMap();
                    }, 50);

                } catch (error) {
                    console.error('Error showing map:', error);
                }
            });
        }

        if (closeMapViewBtn && mapViewContainer && listViewContainer && toggleMapViewBtn) {
            closeMapViewBtn.addEventListener('click', function() {
                try {
                    mapViewContainer.style.display = 'none';
                    listViewContainer.style.display = 'block';

                    // Update button state
                    toggleMapViewBtn.classList.remove('btn-primary');
                    toggleMapViewBtn.classList.add('btn-outline-primary');
                    toggleMapViewBtn.innerHTML = '<i class="bi bi-map"></i> Map';

                } catch (error) {
                    console.error('Error hiding map:', error);
                }
            });
        }

        // District zoom selector event handler
        if (districtZoomSelector) {
            districtZoomSelector.addEventListener('change', function() {
                const selectedDistrict = this.value;
                if (map && selectedDistrict && districtBounds[selectedDistrict]) {
                    const districtData = districtBounds[selectedDistrict];
                    map.setView(districtData.center, districtData.zoom);
                } else if (map && !selectedDistrict) {
                    // Show all markers if no district selected
                    if (markers.length > 0) {
                        const group = new L.featureGroup(markers);
                        map.fitBounds(group.getBounds().pad(0.1));
                    }
                }
            });
        }

        // Clustering toggle event handler
        if (clusteringSelector) {
            clusteringSelector.addEventListener('change', function() {
                if (map && markerClusterGroup) {
                    const clusteringEnabled = this.value !== 'disabled';

                    // Clear all markers from map and cluster group
                    markers.forEach(marker => {
                        map.removeLayer(marker);
                        markerClusterGroup.removeLayer(marker);
                    });

                    // Re-add markers based on clustering setting
                    markers.forEach(marker => {
                        if (clusteringEnabled) {
                            markerClusterGroup.addLayer(marker);
                        } else {
                            marker.addTo(map);
                        }
                    });
                }
            });
        }

        // Map filter selector event handler
        if (mapFilterSelector) {
            mapFilterSelector.addEventListener('change', function() {
                // This would filter markers based on criteria
                // For now, we'll just refresh the map
                if (map) {
                    initMap();
                }
            });
        }

        // Refresh map button event handler
        if (refreshMapBtn) {
            refreshMapBtn.addEventListener('click', function() {
                if (map) {
                    initMap();
                }
            });
        }

        // Make coordinate badges clickable to show on map
        function attachCoordinateBadgeListeners() {
            const badges = getCoordinatesBadges();
            badges.forEach(badge => {
            badge.addEventListener('click', function(e) {
                e.preventDefault();

                try {
                    const lat = parseFloat(this.dataset.lat);
                    const lng = parseFloat(this.dataset.lng);
                    const name = this.dataset.name;

                    if (!isNaN(lat) && !isNaN(lng) && isWithinUganda(lat, lng)) {
                        // Show map view
                        if (mapViewContainer && listViewContainer && toggleMapViewBtn) {
                            mapViewContainer.style.display = 'block';
                            listViewContainer.style.display = 'none';
                            toggleMapViewBtn.classList.add('btn-primary');
                            toggleMapViewBtn.classList.remove('btn-outline-primary');
                            toggleMapViewBtn.innerHTML = '<i class="bi bi-map-fill"></i> Map';
                        }

                        // Initialize map and focus on this location
                        setTimeout(() => {
                            initMap();

                            if (map) {
                                map.setView([lat, lng], 16);

                                // Find and open the popup for this station
                                markers.forEach(marker => {
                                    const markerLatLng = marker.getLatLng();
                                    if (Math.abs(markerLatLng.lat - lat) < 0.0001 &&
                                        Math.abs(markerLatLng.lng - lng) < 0.0001) {
                                        marker.openPopup();
                                    }
                                });
                            }
                        }, 100);
                    } else {
                        alert('Invalid coordinates for this station');
                    }
                } catch (error) {
                    console.error('Error showing station on map:', error);
                }
            });
            });
        }

        // Initialize coordinate badge listeners
        attachCoordinateBadgeListeners();

    });
</script>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.form-control:focus, .form-select:focus {
    border-color: #FFD700;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: none;
    font-weight: 600;
    color: #333;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
    color: #333;
}

.btn-warning {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: none;
    color: #333;
    font-weight: 600;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    color: #333;
}

.text-primary {
    color: #FF8C00 !important;
}

.text-warning {
    color: #FF8C00 !important;
}

.alert-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.bg-light {
    background: linear-gradient(135deg, #fffef7 0%, #f8f9fa 100%) !important;
    border-radius: 8px;
}
</style>

@endsection
