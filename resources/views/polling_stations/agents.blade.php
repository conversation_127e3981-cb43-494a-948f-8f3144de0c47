@extends('layouts.app')

@section('content')
<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
    }
    
    .card-custom {
        border: none;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .card-header-custom {
        background: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.5rem;
    }
    
    .card-body-custom {
        padding: 1.5rem;
    }
    
    .btn-add {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        border: none;
        color: white;
        font-weight: 500;
        padding: 0.6rem 1.25rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 10px rgba(255, 165, 0, 0.2);
        transition: all 0.3s;
    }
    
    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(255, 165, 0, 0.3);
        color: white;
    }
    
    .btn-add i {
        margin-right: 8px;
        font-size: 1rem;
    }
    
    .table-custom {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
        margin-top: 0.5rem;
    }
    
    .table-custom thead th {
        border: none;
        background-color: #f8f9fa;
        color: #6c757d;
        font-weight: 500;
        padding: 0.75rem 1.25rem;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .table-custom tbody tr {
        background-color: #fff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
        border-radius: 8px;
        transition: all 0.2s;
    }
    
    .table-custom tbody tr:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.07);
    }
    
    .table-custom td {
        padding: 1rem 1.25rem;
        vertical-align: middle;
        border: none;
    }
    
    .table-custom td:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }
    
    .table-custom td:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
    }
    
    .agent-name {
        font-weight: 500;
        color: #333;
    }
    
    .agent-phone {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
    }
    
    .btn-action {
        border: none;
        padding: 0.4rem 0.75rem;
        border-radius: 6px;
        font-weight: 500;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        transition: all 0.2s;
    }
    
    .btn-action i {
        margin-right: 5px;
        font-size: 0.9rem;
    }
    
    .btn-edit {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .btn-edit:hover {
        background-color: #0d6efd;
        color: white;
    }
    
    .btn-delete {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    .btn-delete:hover {
        background-color: #dc3545;
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
    }
    
    .empty-state-icon {
        font-size: 3rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }
    
    .empty-state-text {
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 1.5rem;
    }
    
    .modal-content-custom {
        border: none;
        border-radius: 12px;
        overflow: hidden;
    }
    
    .modal-header-custom {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        color: white;
        border: none;
        padding: 1.25rem 1.5rem;
    }
    
    .modal-title-custom {
        font-weight: 600;
        font-size: 1.25rem;
    }
    
    .modal-body-custom {
        padding: 1.5rem;
    }
    
    .form-group {
        margin-bottom: 1.25rem;
    }
    
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    
    .form-control-custom {
        border-radius: 8px;
        padding: 0.75rem 1rem;
        border: 1px solid #dee2e6;
        transition: all 0.2s;
    }
    
    .form-control-custom:focus {
        border-color: #FFA500;
        box-shadow: 0 0 0 0.25rem rgba(255, 165, 0, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        border: none;
        color: white;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(255, 165, 0, 0.3);
    }
    
    .btn-cancel {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #6c757d;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s;
    }
    
    .btn-cancel:hover {
        background-color: #e9ecef;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchTerm = this.value.toLowerCase();
                const tableRows = document.querySelectorAll('.table-custom tbody tr');
                
                tableRows.forEach(row => {
                    const agentName = row.querySelector('.agent-name').textContent.toLowerCase();
                    const agentPhone = row.querySelector('.agent-phone').textContent.toLowerCase();
                    
                    if (agentName.includes(searchTerm) || agentPhone.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
                
                // Check if any results are visible
                const visibleRows = document.querySelectorAll('.table-custom tbody tr[style=""]');
                const noResultsMessage = document.getElementById('noResultsMessage');
                
                if (visibleRows.length === 0 && searchTerm !== '') {
                    if (!noResultsMessage) {
                        const tbody = document.querySelector('.table-custom tbody');
                        const noResults = document.createElement('tr');
                        noResults.id = 'noResultsMessage';
                        noResults.innerHTML = `<td colspan="3" class="text-center py-4">
                            <div class="d-flex flex-column align-items-center">
                                <i class="bi bi-search text-muted mb-2" style="font-size: 2rem;"></i>
                                <p class="mb-0">No agents found matching "${searchTerm}"</p>
                            </div>
                        </td>`;
                        tbody.appendChild(noResults);
                    }
                } else if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            });
        }
    });
</script>
<div class="container">
    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h4 class="page-title">{{ $pollingStation->name }} Agents</h4>
            <p class="text-muted">Manage polling agents for this station</p>
        </div>
        <button type="button" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#add_agents">
            <i class="bi bi-person-plus"></i> Add Agent
        </button>
    </div>
    
    <!-- Main Content Card -->
    <div class="card card-custom">
        <div class="card-header-custom d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <i class="bi bi-people me-2 text-warning"></i>
                <span class="fw-medium">All Agents</span>
            </div>
            <div class="input-group" style="width: 250px;">
                <input type="text" class="form-control" placeholder="Search agents..." id="searchInput">
                <span class="input-group-text bg-light">
                    <i class="bi bi-search"></i>
                </span>
            </div>
        </div>
        <div class="card-body-custom p-0">
            @if(count($agents) > 0)
            <div class="table-responsive">
                <table class="table table-custom">
                    <thead>
                        <tr>
                            <th>Agent</th>
                            <th>Contact</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($agents as $agent)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="d-inline-flex align-items-center justify-content-center bg-light rounded-circle me-3" style="width: 40px; height: 40px;">
                                            <i class="bi bi-person text-warning"></i>
                                        </div>
                                        <span class="agent-name">{{ $agent->user->name }}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-phone me-2 text-muted"></i>
                                        <span class="agent-phone">{{ $agent->user->phone_number }}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons justify-content-end">
                                        <a href="{{ route('agents.edit',$agent->id) }}" class="btn btn-action btn-edit">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a>
                                        @if($agent->votes->count() == 0)
                                        <form action="{{ route('agents.destroy',$agent->id) }}" method="post" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-action btn-delete" onclick="return confirm('Are you sure you want to delete this agent?')">
                                                <i class="bi bi-trash"></i> Delete
                                            </button>
                                        </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>                            
                        @endforeach
                    </tbody>
                </table>
            </div>
            @else
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="bi bi-people"></i>
                </div>
                <h5 class="empty-state-text">No agents found for this polling station</h5>
                <button type="button" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#add_agents">
                    <i class="bi bi-person-plus"></i> Add Your First Agent
                </button>
            </div>
            @endif              
        </div>
    </div>
</div>


<div class="modal fade" id="add_agents" tabindex="-1" aria-labelledby="agentModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content modal-content-custom">
      <div class="modal-header modal-header-custom">
        <h5 class="modal-title modal-title-custom" id="agentModalLabel">
            <i class="bi bi-person-plus me-2"></i> Add New Agent
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body modal-body-custom">
        <form action="{{ route('agents.store') }}" method="post">
            @csrf
            <input type="hidden" value="{{ $pollingStation->id }}" name="polling_station_id">
            
            <div class="form-group">
                <label for="name" class="form-label">Agent Name <span class="text-danger">*</span></label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-person"></i>
                    </span>
                    <input type="text" name="name" id="name" class="form-control form-control-custom" placeholder="Enter agent's full name" required>
                </div>
            </div>

            <div class="form-group">
                <label for="phone_number" class="form-label">Phone Number <span class="text-danger">*</span></label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-phone"></i>
                    </span>
                    <input type="tel" name="phone_number" id="phone_number" class="form-control form-control-custom" placeholder="Enter phone number" required>
                </div>
                <small class="text-muted">This will be used as the agent's login credential</small>
            </div>

            <div class="d-flex justify-content-end gap-2 mt-4">
                <button type="button" class="btn btn-cancel" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i> Cancel
                </button>
                <button type="submit" class="btn btn-save">
                    <i class="bi bi-check-circle me-1"></i> Save Agent
                </button>
            </div>
        </form>
      </div>
    </div>
  </div>
</div>
 
@endsection
