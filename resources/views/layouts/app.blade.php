<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF <PERSON>ken -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

   <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
   <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
   <link href="{{ asset('css/main.css') }}" rel="stylesheet">

   @yield('styles')
   
</head>
<body>
    <div id="app">
        @if(!request()->is('login') && !request()->is('/') && !Auth::guest())
        <nav class="navbar navbar-expand-lg navbar-custom sticky-top">
            <div class="container-fluid px-3">
                <a class="navbar-brand navbar-brand-custom" href="{{ url('/') }}">
                    <div class="brand-icon">
                        <i class="bi bi-check2-circle"></i>
                    </div>
                    {{ config('app.name', 'Vote Count') }}
                </a>
                <button class="navbar-toggler navbar-toggler-custom" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon-custom"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Left Side Of Navbar -->
                    <ul class="navbar-nav me-auto">
                        @guest
                        @else
                            <li class="nav-item">
                                <a class="nav-link nav-link-custom {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">
                                    <i class="bi bi-house-door"></i> {{ __('Dashboard') }}
                                </a>
                            </li>

                            @if(Auth::user()->hasPermission('view_positions'))
                                <li class="nav-item">
                                    <a class="nav-link nav-link-custom {{ request()->routeIs('positions.*') ? 'active' : '' }}" href="{{ route('positions.index') }}">
                                        <i class="bi bi-person-badge"></i> {{ __('Positions') }}
                                    </a>
                                </li>
                            @endif

                            @if(Auth::user()->hasPermission('view_polling_stations'))
                                <li class="nav-item">
                                    <a class="nav-link nav-link-custom {{ request()->routeIs('polling_stations.*') ? 'active' : '' }}" href="{{ route('polling_stations.index') }}">
                                        <i class="bi bi-building"></i> {{ __('Polling Stations') }}
                                    </a>
                                </li>
                            @endif

                            @if(Auth::user()->hasPermission('view_candidates'))
                                <li class="nav-item">
                                    <a class="nav-link nav-link-custom {{ request()->routeIs('monitoring.*') ? 'active' : '' }}" href="{{ route('monitoring.index') }}">
                                        <i class="bi bi-star"></i> {{ __('Monitoring') }}
                                    </a>
                                </li>
                            @endif

                            @if(Auth::user()->hasPermission('view_users'))
                                <li class="nav-item dropdown">
                                    <a class="nav-link nav-link-custom dropdown-toggle {{ request()->routeIs('admin.*') ? 'active' : '' }}"
                                       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-gear"></i> {{ __('Admin') }}
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-custom">
                                        <li>
                                            <a class="dropdown-item dropdown-item-custom" href="{{ route('admin.users.index') }}">
                                                <i class="bi bi-people"></i> {{ __('Users') }}
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item dropdown-item-custom" href="{{ route('admin.roles.index') }}">
                                                <i class="bi bi-shield-check"></i> {{ __('Roles') }}
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item dropdown-item-custom" href="{{ route('admin.permissions.info') }}">
                                                <i class="bi bi-info-circle"></i> {{ __('Permissions Info') }}
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            @endif

                  
                            
                        @endguest
                    </ul>

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Notification Dropdown -->
                        @auth
                            <li class="nav-item dropdown me-2">
                                <a id="notificationDropdown" class="nav-link p-2" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    <i class="bi bi-bell position-relative" style="font-size: 1rem;">
                                        @if(Auth::user()->unreadNotifications->count() > 0)
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.45rem; padding: 2px 4px;">
                                                {{ Auth::user()->unreadNotifications->count() }}
                                            </span>
                                        @endif
                                    </i>
                                </a>

                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationDropdown" style="width: 350px; max-height: 400px; overflow-y: auto;">
                                    <div class="d-flex justify-content-between align-items-center px-3 py-2 border-bottom">
                                        <h6 class="mb-0">Notifications</h6>
                                        @if(Auth::user()->unreadNotifications->count() > 0)
                                            <form action="{{ route('notifications.read-all') }}" method="POST">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-link p-0 text-decoration-none">Mark all as read</button>
                                            </form>
                                        @endif
                                    </div>
                                    
                                    @if(Auth::user()->unreadNotifications->count() > 0)
                                        @foreach(Auth::user()->unreadNotifications->take(5) as $notification)
                                            @php
                                                $data = $notification->data;
                                                $isVoteGapAlert = isset($data['candidate_id']);
                                            @endphp
                                            <div class="dropdown-item border-bottom py-2 px-3 bg-light">
                                                @if($isVoteGapAlert)
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <p class="mb-1 fw-bold">
                                                                <i class="bi bi-exclamation-triangle-fill text-warning me-1"></i>
                                                                {{ $data['candidate_name'] }}
                                                            </p>
                                                            <p class="mb-1 small">
                                                                {{ $data['is_leading'] 
                                                                    ? "Lead has fallen to only {$data['gap']} votes." 
                                                                    : "Trailing by {$data['gap']} votes." 
                                                                }}
                                                            </p>
                                                            <p class="mb-0 text-muted" style="font-size: 0.75rem;">
                                                                {{ $notification->created_at->diffForHumans() }}
                                                            </p>
                                                        </div>
                                                        <div>
                                                            <a href="{{ route('monitoring.compare', $data['candidate_id']) }}" class="btn btn-sm btn-primary">
                                                                View
                                                            </a>
                                                        </div>
                                                    </div>
                                                @else
                                                    <p class="mb-1">{{ json_encode($data) }}</p>
                                                    <p class="mb-0 text-muted" style="font-size: 0.75rem;">
                                                        {{ $notification->created_at->diffForHumans() }}
                                                    </p>
                                                @endif
                                            </div>
                                        @endforeach
                                        <a href="{{ route('notifications.index') }}" class="dropdown-item text-center py-2 text-primary">
                                            View all notifications
                                        </a>
                                    @else
                                        <div class="dropdown-item text-center py-3">
                                            <i class="bi bi-bell-slash text-muted d-block mb-2" style="font-size: 1.5rem;"></i>
                                            <p class="mb-0 text-muted">No new notifications</p>
                                        </div>
                                    @endif
                                </div>
                            </li>
                        @endauth
                        
                        <!-- Authentication Links -->
                        @guest
                            @if (Route::has('login'))
                                <li class="nav-item">
                                    <a class="nav-link nav-link-custom" href="{{ route('login') }}">
                                        <i class="bi bi-box-arrow-in-right"></i> {{ __('Login') }}
                                    </a>
                                </li>
                            @endif                           
                        @else
                            <li class="nav-item dropdown">
                                <a id="navbarDropdown" class="nav-link p-0" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    <div class="user-dropdown">
                                        <div class="user-avatar">
                                            {{ substr(Auth::user()->name, 0, 1) }}
                                        </div>
                                        <span class="user-name d-none d-sm-inline">{{ Auth::user()->name }}</span>
                                        <i class="bi bi-chevron-down"></i>
                                    </div>
                                </a>

                                <div class="dropdown-menu dropdown-menu-custom dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <div class="px-4 py-3 text-center">
                                        <div class="user-avatar mx-auto mb-2" style="width: 60px; height: 60px; font-size: 1.5rem;">
                                            {{ substr(Auth::user()->name, 0, 1) }}
                                        </div>
                                        <h6 class="mb-0">{{ Auth::user()->name }}</h6>
                                        <small class="text-muted">{{ Auth::user()->user_type }}</small>
                                    </div>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item dropdown-item-custom" href="{{ route('users.create') }}">
                                        <i class="bi bi-person"></i> {{ __('Change Password') }}
                                    </a>
                                    {{-- <a class="dropdown-item dropdown-item-custom" href="#">
                                        <i class="bi bi-gear"></i> {{ __('Settings') }}
                                    </a>  --}}
                                    <a class="dropdown-item dropdown-item-custom logout" href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                        <i class="bi bi-box-arrow-right"></i> {{ __('Logout') }}
                                    </a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>
        @endif

        <main class="main-content">
            <div class="container">
                @if (session('status'))
                    <div class="alert alert-success alert-custom" role="alert">
                        <i class="bi bi-check-circle me-2"></i> {{ session('status') }}
                    </div>
                @endif
                @yield('content')
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js" integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous"></script>
    <script src="{{ asset('js/smooth-refresh.js') }}"></script>

    <!-- Smooth Auto-Refresh System -->
    <script>
        class SmoothRefreshManager {
            constructor() {
                this.refreshInterval = null;
                this.refreshRate = 60000; // 60 seconds - reasonable interval
                this.isRefreshing = false;
                this.lastRefreshTime = Date.now();
                this.refreshIndicator = null;
                this.init();
            }

            init() {
                this.createRefreshIndicator();
                this.startAutoRefresh();
                this.setupEventListeners();
                this.setupVisibilityChange();
            }

            createRefreshIndicator() {
                // Create a subtle refresh indicator
                this.refreshIndicator = document.createElement('div');
                this.refreshIndicator.id = 'refresh-indicator';
                this.refreshIndicator.innerHTML = `
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status" style="width: 12px; height: 12px;">
                            <span class="visually-hidden">Updating...</span>
                        </div>
                        <small>Updating data...</small>
                    </div>
                `;
                this.refreshIndicator.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(40, 167, 69, 0.9);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    z-index: 9999;
                    opacity: 0;
                    transform: translateY(-10px);
                    transition: all 0.3s ease;
                    pointer-events: none;
                `;
                document.body.appendChild(this.refreshIndicator);
            }

            showRefreshIndicator() {
                if (this.refreshIndicator) {
                    this.refreshIndicator.style.opacity = '1';
                    this.refreshIndicator.style.transform = 'translateY(0)';
                }
            }

            hideRefreshIndicator() {
                if (this.refreshIndicator) {
                    this.refreshIndicator.style.opacity = '0';
                    this.refreshIndicator.style.transform = 'translateY(-10px)';
                }
            }

            showRefreshError() {
                // Show a temporary error message
                const errorIndicator = document.createElement('div');
                errorIndicator.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <small>Update failed - <a href="#" onclick="window.location.reload()" class="text-white text-decoration-underline">Refresh page</a></small>
                    </div>
                `;
                errorIndicator.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(220, 53, 69, 0.9);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    z-index: 9999;
                    opacity: 1;
                    transform: translateY(0);
                    transition: all 0.3s ease;
                `;
                document.body.appendChild(errorIndicator);

                // Remove after 5 seconds
                setTimeout(() => {
                    errorIndicator.style.opacity = '0';
                    errorIndicator.style.transform = 'translateY(-10px)';
                    setTimeout(() => document.body.removeChild(errorIndicator), 300);
                }, 5000);
            }

            showSuccessIndicator() {
                // Briefly change the refresh indicator to show success
                if (this.refreshIndicator) {
                    const originalContent = this.refreshIndicator.innerHTML;
                    const originalBackground = this.refreshIndicator.style.background;

                    this.refreshIndicator.innerHTML = `
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle me-2"></i>
                            <small>Updated!</small>
                        </div>
                    `;
                    this.refreshIndicator.style.background = 'rgba(40, 167, 69, 0.9)';

                    setTimeout(() => {
                        this.refreshIndicator.innerHTML = originalContent;
                        this.refreshIndicator.style.background = originalBackground;
                    }, 1500);
                }
            }

            shouldSkipRefresh() {
                // Don't refresh if map is open
                const mapContainer = document.getElementById('mapViewContainer');
                if (mapContainer && mapContainer.style.display !== 'none') {
                    console.log('Auto-refresh paused: Map is open');
                    return true;
                }

                // Don't refresh if user is actively interacting
                if (document.activeElement && (
                    document.activeElement.tagName === 'INPUT' ||
                    document.activeElement.tagName === 'TEXTAREA' ||
                    document.activeElement.tagName === 'SELECT' ||
                    document.activeElement.isContentEditable
                )) {
                    console.log('Auto-refresh paused: User is typing');
                    return true;
                }

                // Don't refresh if modal is open
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    console.log('Auto-refresh paused: Modal is open');
                    return true;
                }

                // Don't refresh if dropdown is open
                const openDropdown = document.querySelector('.dropdown-menu.show');
                if (openDropdown) {
                    console.log('Auto-refresh paused: Dropdown is open');
                    return true;
                }

                return false;
            }

            async performSmoothRefresh() {
                if (this.isRefreshing || this.shouldSkipRefresh()) {
                    return;
                }

                this.isRefreshing = true;
                this.showRefreshIndicator();

                try {
                    // Simple page reload approach - more reliable
                    const currentUrl = window.location.href;
                    const response = await fetch(currentUrl, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'text/html',
                            'Cache-Control': 'no-cache'
                        },
                        credentials: 'same-origin'
                    });

                    if (response.ok) {
                        const newHtml = await response.text();

                        // Parse the new HTML
                        const parser = new DOMParser();
                        const newDoc = parser.parseFromString(newHtml, 'text/html');

                        // Update main content area
                        const currentMain = document.querySelector('.main-content');
                        const newMain = newDoc.querySelector('.main-content');

                        if (currentMain && newMain) {
                            // Smooth transition
                            currentMain.style.opacity = '0.7';
                            setTimeout(() => {
                                currentMain.innerHTML = newMain.innerHTML;
                                currentMain.style.opacity = '1';

                                // Re-initialize any JavaScript components if needed
                                this.reinitializeComponents();
                            }, 200);
                        }

                        // Update notifications count
                        await this.refreshNotifications();

                        this.lastRefreshTime = Date.now();
                        console.log('✅ Page refreshed successfully at', new Date().toLocaleTimeString());

                        // Show subtle success feedback
                        this.showSuccessIndicator();

                    } else {
                        console.warn('Refresh failed with status:', response.status);
                    }

                } catch (error) {
                    console.error('Smooth refresh failed:', error);

                    // Fallback: show a simple message and offer manual refresh
                    this.showRefreshError();
                } finally {
                    this.isRefreshing = false;
                    setTimeout(() => this.hideRefreshIndicator(), 1000);
                }
            }

            reinitializeComponents() {
                // Re-initialize any JavaScript components that might be needed
                try {
                    // Re-initialize charts if they exist
                    if (typeof loadChartData === 'function') {
                        const currentPosition = document.querySelector('.position-tab.active')?.dataset.position || 'overall';
                        setTimeout(() => loadChartData(currentPosition), 100);
                    }

                    // Re-initialize any other components
                    if (typeof initializeTooltips === 'function') {
                        initializeTooltips();
                    }

                    // Re-initialize Bootstrap components
                    if (window.bootstrap) {
                        // Re-initialize tooltips
                        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                        tooltipTriggerList.map(function (tooltipTriggerEl) {
                            return new bootstrap.Tooltip(tooltipTriggerEl);
                        });
                    }

                } catch (error) {
                    console.error('Failed to reinitialize components:', error);
                }
            }

            async refreshNotifications() {
                try {
                    // Check if notifications endpoint exists
                    const response = await fetch('/notifications/count', {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        this.updateNotificationBadge(data.count || 0);
                    }
                } catch (error) {
                    // Silently fail if notifications endpoint doesn't exist
                    console.log('Notifications endpoint not available');
                }
            }



            updateNotificationBadge(count) {
                const badge = document.querySelector('#notificationDropdown .badge');
                if (badge) {
                    if (count > 0) {
                        badge.textContent = count;
                        badge.style.display = 'inline';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            }

            animateNumber(element, targetNumber) {
                // Use enhanced animation if available, fallback to simple version
                if (window.smoothRefreshUtils && window.smoothRefreshUtils.enhancedAnimateNumber) {
                    window.smoothRefreshUtils.enhancedAnimateNumber(element, targetNumber);
                    return;
                }

                // Fallback simple animation
                if (!element) return;

                const currentNumber = parseInt(element.textContent) || 0;
                const increment = (targetNumber - currentNumber) / 20;
                let current = currentNumber;

                const timer = setInterval(() => {
                    current += increment;
                    if ((increment > 0 && current >= targetNumber) || (increment < 0 && current <= targetNumber)) {
                        current = targetNumber;
                        clearInterval(timer);
                    }
                    element.textContent = Math.round(current);
                }, 50);
            }

            startAutoRefresh() {
                this.refreshInterval = setInterval(() => {
                    this.performSmoothRefresh();
                }, this.refreshRate);

                console.log(`Smooth auto-refresh started (${this.refreshRate/1000}s interval)`);
            }

            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                    console.log('Auto-refresh stopped');
                }
            }

            setupEventListeners() {
                // Stop auto-refresh when map is opened
                const toggleMapBtn = document.getElementById('toggleMapView');
                if (toggleMapBtn) {
                    toggleMapBtn.addEventListener('click', () => this.stopAutoRefresh());
                }

                // Resume auto-refresh when map is closed
                const closeMapBtn = document.getElementById('closeMapView');
                if (closeMapBtn) {
                    closeMapBtn.addEventListener('click', () => this.startAutoRefresh());
                }

                // Manual refresh button
                this.addManualRefreshButton();
            }

            setupVisibilityChange() {
                // Pause refresh when tab is not visible
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        this.stopAutoRefresh();
                    } else {
                        this.startAutoRefresh();
                        // Refresh immediately when tab becomes visible
                        setTimeout(() => this.performSmoothRefresh(), 1000);
                    }
                });
            }

            addManualRefreshButton() {
                // Add a manual refresh button to the navbar
                const navbar = document.querySelector('.navbar-nav.ms-auto');
                if (navbar) {
                    const refreshBtn = document.createElement('li');
                    refreshBtn.className = 'nav-item me-2';
                    refreshBtn.innerHTML = `
                        <button class="btn btn-outline-secondary btn-sm" id="manual-refresh-btn" title="Refresh page data" style="border-radius: 6px; padding: 0.4rem 0.6rem;">
                            <i class="bi bi-arrow-clockwise" style="font-size: 0.9rem;"></i>
                        </button>
                    `;

                    const refreshButton = refreshBtn.querySelector('#manual-refresh-btn');
                    refreshButton.addEventListener('click', () => {
                        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise spinning" style="font-size: 0.9rem;"></i>';
                        refreshButton.disabled = true;

                        this.performSmoothRefresh().finally(() => {
                            refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise" style="font-size: 0.9rem;"></i>';
                            refreshButton.disabled = false;
                        });
                    });

                    navbar.insertBefore(refreshBtn, navbar.firstChild);
                }
            }
        }

        // Initialize smooth refresh system when page loads
        document.addEventListener('DOMContentLoaded', function() {
            window.smoothRefresh = new SmoothRefreshManager();
        });

        // Add CSS for spinning animation
        const style = document.createElement('style');
        style.textContent = `
            .spinning {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>

    @stack('scripts')
</body>
</html>
