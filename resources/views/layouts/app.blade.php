<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF <PERSON>ken -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

   <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
   <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
   <link href="{{ asset('css/main.css') }}" rel="stylesheet">

   @yield('styles')
   
</head>
<body>
    <div id="app">
        @if(!request()->is('login') && !request()->is('/') && !Auth::guest())
        <nav class="navbar navbar-expand-lg navbar-custom sticky-top">
            <div class="container">
                <a class="navbar-brand navbar-brand-custom" href="{{ url('/') }}">
                    <div class="brand-icon">
                        <i class="bi bi-check2-circle"></i>
                    </div>
                    {{ config('app.name', 'Vote Count') }}
                </a>
                <button class="navbar-toggler navbar-toggler-custom" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon-custom"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Left Side Of Navbar -->
                    <ul class="navbar-nav me-auto">
                        @guest
                        @else
                            <li class="nav-item">
                                <a class="nav-link nav-link-custom {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">
                                    <i class="bi bi-house-door"></i> {{ __('Dashboard') }}
                                </a>
                            </li>

                            @if(Auth::user()->user_type == "admin")

                                <li class="nav-item">
                                    <a class="nav-link nav-link-custom {{ request()->routeIs('positions.*') ? 'active' : '' }}" href="{{ route('positions.index') }}">
                                        <i class="bi bi-person-badge"></i> {{ __('Positions') }}
                                    </a>
                                </li>

                                <li class="nav-item">
                                    <a class="nav-link nav-link-custom {{ request()->routeIs('polling_stations.*') ? 'active' : '' }}" href="{{ route('polling_stations.index') }}">
                                        <i class="bi bi-building"></i> {{ __('Polling Stations') }}
                                    </a>
                                </li>
                                
                                <li class="nav-item">
                                    <a class="nav-link nav-link-custom {{ request()->routeIs('monitoring.*') ? 'active' : '' }}" href="{{ route('monitoring.index') }}">
                                        <i class="bi bi-star"></i> {{ __('Monitoring') }}
                                    </a>
                                </li>

                            @endif

                  
                            
                        @endguest
                    </ul>

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Notification Dropdown -->
                        @auth
                            <li class="nav-item dropdown me-3">
                                <a id="notificationDropdown" class="nav-link" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    <i class="bi bi-bell position-relative" style="font-size: 1.2rem;">
                                        @if(Auth::user()->unreadNotifications->count() > 0)
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.5rem;">
                                                {{ Auth::user()->unreadNotifications->count() }}
                                            </span>
                                        @endif
                                    </i>
                                </a>

                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationDropdown" style="width: 350px; max-height: 400px; overflow-y: auto;">
                                    <div class="d-flex justify-content-between align-items-center px-3 py-2 border-bottom">
                                        <h6 class="mb-0">Notifications</h6>
                                        @if(Auth::user()->unreadNotifications->count() > 0)
                                            <form action="{{ route('notifications.read-all') }}" method="POST">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-link p-0 text-decoration-none">Mark all as read</button>
                                            </form>
                                        @endif
                                    </div>
                                    
                                    @if(Auth::user()->unreadNotifications->count() > 0)
                                        @foreach(Auth::user()->unreadNotifications->take(5) as $notification)
                                            @php
                                                $data = $notification->data;
                                                $isVoteGapAlert = isset($data['candidate_id']);
                                            @endphp
                                            <div class="dropdown-item border-bottom py-2 px-3 bg-light">
                                                @if($isVoteGapAlert)
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <p class="mb-1 fw-bold">
                                                                <i class="bi bi-exclamation-triangle-fill text-warning me-1"></i>
                                                                {{ $data['candidate_name'] }}
                                                            </p>
                                                            <p class="mb-1 small">
                                                                {{ $data['is_leading'] 
                                                                    ? "Lead has fallen to only {$data['gap']} votes." 
                                                                    : "Trailing by {$data['gap']} votes." 
                                                                }}
                                                            </p>
                                                            <p class="mb-0 text-muted" style="font-size: 0.75rem;">
                                                                {{ $notification->created_at->diffForHumans() }}
                                                            </p>
                                                        </div>
                                                        <div>
                                                            <a href="{{ route('monitoring.compare', $data['candidate_id']) }}" class="btn btn-sm btn-primary">
                                                                View
                                                            </a>
                                                        </div>
                                                    </div>
                                                @else
                                                    <p class="mb-1">{{ json_encode($data) }}</p>
                                                    <p class="mb-0 text-muted" style="font-size: 0.75rem;">
                                                        {{ $notification->created_at->diffForHumans() }}
                                                    </p>
                                                @endif
                                            </div>
                                        @endforeach
                                        <a href="{{ route('notifications.index') }}" class="dropdown-item text-center py-2 text-primary">
                                            View all notifications
                                        </a>
                                    @else
                                        <div class="dropdown-item text-center py-3">
                                            <i class="bi bi-bell-slash text-muted d-block mb-2" style="font-size: 1.5rem;"></i>
                                            <p class="mb-0 text-muted">No new notifications</p>
                                        </div>
                                    @endif
                                </div>
                            </li>
                        @endauth
                        
                        <!-- Authentication Links -->
                        @guest
                            @if (Route::has('login'))
                                <li class="nav-item">
                                    <a class="nav-link nav-link-custom" href="{{ route('login') }}">
                                        <i class="bi bi-box-arrow-in-right"></i> {{ __('Login') }}
                                    </a>
                                </li>
                            @endif                           
                        @else
                            <li class="nav-item dropdown">
                                <a id="navbarDropdown" class="nav-link p-0" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    <div class="user-dropdown">
                                        <div class="user-avatar">
                                            {{ substr(Auth::user()->name, 0, 1) }}
                                        </div>
                                        <span class="user-name d-none d-sm-inline">{{ Auth::user()->name }}</span>
                                        <i class="bi bi-chevron-down"></i>
                                    </div>
                                </a>

                                <div class="dropdown-menu dropdown-menu-custom dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <div class="px-4 py-3 text-center">
                                        <div class="user-avatar mx-auto mb-2" style="width: 60px; height: 60px; font-size: 1.5rem;">
                                            {{ substr(Auth::user()->name, 0, 1) }}
                                        </div>
                                        <h6 class="mb-0">{{ Auth::user()->name }}</h6>
                                        <small class="text-muted">{{ Auth::user()->user_type }}</small>
                                    </div>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item dropdown-item-custom" href="{{ route('users.create') }}">
                                        <i class="bi bi-person"></i> {{ __('Change Password') }}
                                    </a>
                                    {{-- <a class="dropdown-item dropdown-item-custom" href="#">
                                        <i class="bi bi-gear"></i> {{ __('Settings') }}
                                    </a>  --}}
                                    <a class="dropdown-item dropdown-item-custom logout" href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                        <i class="bi bi-box-arrow-right"></i> {{ __('Logout') }}
                                    </a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>
        @endif

        <main class="main-content">
            <div class="container">
                @if (session('status'))
                    <div class="alert alert-success alert-custom" role="alert">
                        <i class="bi bi-check-circle me-2"></i> {{ session('status') }}
                    </div>
                @endif
                @yield('content')
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js" integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous"></script>
    <script src="{{ asset('js/smooth-refresh.js') }}"></script>

    <!-- Smooth Auto-Refresh System -->
    <script>
        class SmoothRefreshManager {
            constructor() {
                this.refreshInterval = null;
                this.refreshRate = 30000; // 30 seconds - more frequent but smoother
                this.isRefreshing = false;
                this.lastRefreshTime = Date.now();
                this.refreshIndicator = null;
                this.init();
            }

            init() {
                this.createRefreshIndicator();
                this.startAutoRefresh();
                this.setupEventListeners();
                this.setupVisibilityChange();
            }

            createRefreshIndicator() {
                // Create a subtle refresh indicator
                this.refreshIndicator = document.createElement('div');
                this.refreshIndicator.id = 'refresh-indicator';
                this.refreshIndicator.innerHTML = `
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status" style="width: 12px; height: 12px;">
                            <span class="visually-hidden">Updating...</span>
                        </div>
                        <small>Updating data...</small>
                    </div>
                `;
                this.refreshIndicator.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(40, 167, 69, 0.9);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    z-index: 9999;
                    opacity: 0;
                    transform: translateY(-10px);
                    transition: all 0.3s ease;
                    pointer-events: none;
                `;
                document.body.appendChild(this.refreshIndicator);
            }

            showRefreshIndicator() {
                if (this.refreshIndicator) {
                    this.refreshIndicator.style.opacity = '1';
                    this.refreshIndicator.style.transform = 'translateY(0)';
                }
            }

            hideRefreshIndicator() {
                if (this.refreshIndicator) {
                    this.refreshIndicator.style.opacity = '0';
                    this.refreshIndicator.style.transform = 'translateY(-10px)';
                }
            }

            shouldSkipRefresh() {
                // Don't refresh if map is open
                const mapContainer = document.getElementById('mapViewContainer');
                if (mapContainer && mapContainer.style.display !== 'none') {
                    console.log('Auto-refresh paused: Map is open');
                    return true;
                }

                // Don't refresh if user is actively interacting
                if (document.activeElement && (
                    document.activeElement.tagName === 'INPUT' ||
                    document.activeElement.tagName === 'TEXTAREA' ||
                    document.activeElement.tagName === 'SELECT' ||
                    document.activeElement.isContentEditable
                )) {
                    console.log('Auto-refresh paused: User is typing');
                    return true;
                }

                // Don't refresh if modal is open
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    console.log('Auto-refresh paused: Modal is open');
                    return true;
                }

                // Don't refresh if dropdown is open
                const openDropdown = document.querySelector('.dropdown-menu.show');
                if (openDropdown) {
                    console.log('Auto-refresh paused: Dropdown is open');
                    return true;
                }

                return false;
            }

            async performSmoothRefresh() {
                if (this.isRefreshing || this.shouldSkipRefresh()) {
                    return;
                }

                this.isRefreshing = true;
                this.showRefreshIndicator();

                try {
                    // Update different sections based on current page
                    const currentPath = window.location.pathname;

                    if (currentPath === '/home' || currentPath.includes('home')) {
                        await this.refreshDashboardData();
                    } else if (currentPath.includes('monitoring')) {
                        await this.refreshMonitoringData();
                    } else if (currentPath.includes('polling_stations')) {
                        await this.refreshPollingStationsData();
                    }

                    // Always refresh notifications
                    await this.refreshNotifications();

                    this.lastRefreshTime = Date.now();
                    console.log('Smooth refresh completed successfully');

                } catch (error) {
                    console.error('Refresh failed:', error);
                } finally {
                    this.isRefreshing = false;
                    setTimeout(() => this.hideRefreshIndicator(), 1000);
                }
            }

            async refreshDashboardData() {
                try {
                    // Refresh vote trends chart if it exists
                    if (typeof loadChartData === 'function') {
                        const currentPosition = document.querySelector('.position-tab.active')?.dataset.position || 'overall';
                        loadChartData(currentPosition);
                    }

                    // Refresh dashboard statistics
                    const response = await fetch('/api/dashboard-stats', {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        this.updateDashboardStats(data.stats);
                        this.updateRecentActivity(data.recent_votes);
                    }
                } catch (error) {
                    console.error('Failed to refresh dashboard data:', error);
                }
            }

            async refreshMonitoringData() {
                try {
                    const response = await fetch('/api/monitoring-data', {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        this.updateMonitoringStats(data.stats);
                        this.updateCandidateCards(data.candidates, data.monitoring_data);
                    }
                } catch (error) {
                    console.error('Failed to refresh monitoring data:', error);
                }
            }

            async refreshPollingStationsData() {
                // Refresh map data if map is visible
                if (typeof refreshMapData === 'function') {
                    refreshMapData();
                }
            }

            updateDashboardStats(stats) {
                // Update statistics cards with smooth animations
                this.updateStatCard('[data-stat="total-votes"]', stats.total_votes);
                this.updateStatCard('[data-stat="total-candidates"]', stats.total_candidates);
                this.updateStatCard('[data-stat="total-positions"]', stats.total_positions);
                this.updateStatCard('[data-stat="total-stations"]', stats.total_polling_stations);
                this.updateStatCard('[data-stat="stations-with-results"]', stats.stations_with_results);
            }

            updateStatCard(selector, newValue) {
                const card = document.querySelector(selector);
                if (card) {
                    const valueElement = card.querySelector('.fw-bold, .stat-value');
                    if (valueElement) {
                        this.animateNumber(valueElement, newValue);
                    }
                }
            }

            updateMonitoringStats(stats) {
                // Update monitoring statistics
                this.updateStatCard('[data-stat="leading-count"]', stats.leading_count);
                this.updateStatCard('[data-stat="trailing-count"]', stats.trailing_count);
                this.updateStatCard('[data-stat="alerts-count"]', stats.alerts_count);
            }

            updateCandidateCards(candidates, monitoringData) {
                // Update individual candidate monitoring cards
                candidates.forEach(candidate => {
                    const cardElement = document.querySelector(`[data-candidate-id="${candidate.id}"]`);
                    if (cardElement && monitoringData[candidate.id]) {
                        const gapInfo = monitoringData[candidate.id];

                        // Update vote counts
                        const voteElement = cardElement.querySelector('.candidate-votes');
                        if (voteElement) {
                            this.animateNumber(voteElement, gapInfo.preferred_votes);
                        }

                        // Update gap information
                        const gapElement = cardElement.querySelector('.vote-gap');
                        if (gapElement) {
                            gapElement.textContent = gapInfo.gap;
                        }

                        // Update status indicators
                        this.updateCandidateStatus(cardElement, gapInfo);
                    }
                });
            }

            updateCandidateStatus(cardElement, gapInfo) {
                // Update status badge
                const statusBadge = cardElement.querySelector('.status-badge');
                if (statusBadge) {
                    statusBadge.className = `badge ${gapInfo.is_leading ? 'bg-success' : 'bg-danger'}`;
                    statusBadge.textContent = gapInfo.is_leading ? 'Leading' : 'Trailing';
                }

                // Update progress bar if exists
                const progressBar = cardElement.querySelector('.progress-bar');
                if (progressBar && gapInfo.preferred_votes && gapInfo.competitor_votes) {
                    const total = gapInfo.preferred_votes + gapInfo.competitor_votes;
                    const percentage = (gapInfo.preferred_votes / total) * 100;
                    progressBar.style.width = `${percentage}%`;
                }
            }

            updateRecentActivity(recentVotes) {
                // Update recent activity section if it exists
                const activityContainer = document.querySelector('#recent-activity');
                if (activityContainer && recentVotes.length > 0) {
                    // Add subtle indication of new activity
                    activityContainer.classList.add('updated');
                    setTimeout(() => activityContainer.classList.remove('updated'), 2000);
                }
            }

            async refreshNotifications() {
                try {
                    const response = await fetch('/notifications/count', {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        this.updateNotificationBadge(data.count);
                    }
                } catch (error) {
                    console.error('Failed to refresh notifications:', error);
                }
            }

            updateNotificationBadge(count) {
                const badge = document.querySelector('#notificationDropdown .badge');
                if (badge) {
                    if (count > 0) {
                        badge.textContent = count;
                        badge.style.display = 'inline';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            }

            animateNumber(element, targetNumber) {
                // Use enhanced animation if available, fallback to simple version
                if (window.smoothRefreshUtils && window.smoothRefreshUtils.enhancedAnimateNumber) {
                    window.smoothRefreshUtils.enhancedAnimateNumber(element, targetNumber);
                    return;
                }

                // Fallback simple animation
                if (!element) return;

                const currentNumber = parseInt(element.textContent) || 0;
                const increment = (targetNumber - currentNumber) / 20;
                let current = currentNumber;

                const timer = setInterval(() => {
                    current += increment;
                    if ((increment > 0 && current >= targetNumber) || (increment < 0 && current <= targetNumber)) {
                        current = targetNumber;
                        clearInterval(timer);
                    }
                    element.textContent = Math.round(current);
                }, 50);
            }

            startAutoRefresh() {
                this.refreshInterval = setInterval(() => {
                    this.performSmoothRefresh();
                }, this.refreshRate);

                console.log(`Smooth auto-refresh started (${this.refreshRate/1000}s interval)`);
            }

            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                    console.log('Auto-refresh stopped');
                }
            }

            setupEventListeners() {
                // Stop auto-refresh when map is opened
                const toggleMapBtn = document.getElementById('toggleMapView');
                if (toggleMapBtn) {
                    toggleMapBtn.addEventListener('click', () => this.stopAutoRefresh());
                }

                // Resume auto-refresh when map is closed
                const closeMapBtn = document.getElementById('closeMapView');
                if (closeMapBtn) {
                    closeMapBtn.addEventListener('click', () => this.startAutoRefresh());
                }

                // Manual refresh button
                this.addManualRefreshButton();
            }

            setupVisibilityChange() {
                // Pause refresh when tab is not visible
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        this.stopAutoRefresh();
                    } else {
                        this.startAutoRefresh();
                        // Refresh immediately when tab becomes visible
                        setTimeout(() => this.performSmoothRefresh(), 1000);
                    }
                });
            }

            addManualRefreshButton() {
                // Add a manual refresh button to the navbar
                const navbar = document.querySelector('.navbar-nav.ms-auto');
                if (navbar) {
                    const refreshBtn = document.createElement('li');
                    refreshBtn.className = 'nav-item me-2';
                    refreshBtn.innerHTML = `
                        <button class="btn btn-outline-light btn-sm" id="manual-refresh-btn" title="Refresh data">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    `;

                    const refreshButton = refreshBtn.querySelector('#manual-refresh-btn');
                    refreshButton.addEventListener('click', () => {
                        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
                        refreshButton.classList.add('spinning');
                        this.performSmoothRefresh().finally(() => {
                            refreshButton.classList.remove('spinning');
                        });
                    });

                    navbar.insertBefore(refreshBtn, navbar.firstChild);
                }
            }
        }

        // Initialize smooth refresh system when page loads
        document.addEventListener('DOMContentLoaded', function() {
            window.smoothRefresh = new SmoothRefreshManager();
        });

        // Add CSS for spinning animation
        const style = document.createElement('style');
        style.textContent = `
            .spinning {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>

    @stack('scripts')
</body>
</html>
