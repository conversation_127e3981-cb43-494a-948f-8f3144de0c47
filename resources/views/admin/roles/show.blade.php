@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-shield-check text-primary me-2"></i>
                        Role Details: {{ $role->display_name }}
                    </h4>
                    <div class="d-flex gap-2">
                        @if(auth()->user()->hasPermission('manage_user_roles'))
                        <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-primary">
                            <i class="bi bi-pencil me-1"></i>
                            Edit Role
                        </a>
                        @endif
                        <a href="{{ route('admin.roles.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>
                            Back to Roles
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Role Information -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-info-circle text-info me-2"></i>
                                        Role Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Name:</strong></div>
                                        <div class="col-sm-8">{{ $role->name }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Display Name:</strong></div>
                                        <div class="col-sm-8">{{ $role->display_name }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Description:</strong></div>
                                        <div class="col-sm-8">{{ $role->description ?: 'No description provided' }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Status:</strong></div>
                                        <div class="col-sm-8">
                                            @if($role->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-danger">Inactive</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Users Count:</strong></div>
                                        <div class="col-sm-8">
                                            <span class="badge bg-info">{{ $role->users->count() }} users</span>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Created:</strong></div>
                                        <div class="col-sm-8">{{ $role->created_at->format('M d, Y H:i') }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Permissions -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-key text-warning me-2"></i>
                                        Permissions ({{ count($role->permissions ?? []) }})
                                    </h5>
                                </div>
                                <div class="card-body">
                                    @if($role->permissions && count($role->permissions) > 0)
                                        <div class="row">
                                            @foreach($role->permissions as $permission)
                                                <div class="col-12 mb-2">
                                                    <span class="badge bg-primary">
                                                        <i class="bi bi-check-circle me-1"></i>
                                                        {{ ucwords(str_replace('_', ' ', $permission)) }}
                                                    </span>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="alert alert-warning">
                                            <i class="bi bi-exclamation-triangle me-2"></i>
                                            No permissions assigned to this role
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Users with this role -->
                    @if($role->users->count() > 0)
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-people text-secondary me-2"></i>
                                        Users with this Role ({{ $role->users->count() }})
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Phone</th>
                                                    <th>User Type</th>
                                                    <th>Status</th>
                                                    <th>Last Login</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($role->users as $user)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                                <span class="text-white fw-bold">{{ strtoupper(substr($user->name, 0, 1)) }}</span>
                                                            </div>
                                                            {{ $user->name }}
                                                        </div>
                                                    </td>
                                                    <td>{{ $user->phone_number }}</td>
                                                    <td><span class="badge bg-secondary">{{ ucfirst($user->user_type) }}</span></td>
                                                    <td>
                                                        @if($user->is_active)
                                                            <span class="badge bg-success">Active</span>
                                                        @else
                                                            <span class="badge bg-danger">Inactive</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($user->last_login_at)
                                                            {{ $user->last_login_at->format('M d, Y H:i') }}
                                                        @else
                                                            <span class="text-muted">Never</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if(auth()->user()->hasPermission('view_users'))
                                                        <a href="{{ route('admin.users.show', $user) }}" class="btn btn-sm btn-outline-info">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        @endif
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
}
</style>
@endsection
