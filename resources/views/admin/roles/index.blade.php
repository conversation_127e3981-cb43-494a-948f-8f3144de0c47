@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-shield-check text-primary me-2"></i>
                        Roles & Permissions
                    </h4>
                    @if(auth()->user()->hasPermission('manage_user_roles'))
                    <a href="{{ route('admin.roles.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>
                        Add New Role
                    </a>
                    @endif
                </div>

                <div class="card-body">
                    <div class="row">
                        @foreach($roles as $role)
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-{{ $role->name === 'admin' ? 'danger' : ($role->name === 'polling_station_manager' ? 'warning' : ($role->name === 'agent' ? 'success' : 'info')) }}">
                                <div class="card-header bg-{{ $role->name === 'admin' ? 'danger' : ($role->name === 'polling_station_manager' ? 'warning' : ($role->name === 'agent' ? 'success' : 'info')) }} text-white">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">{{ $role->display_name }}</h6>
                                        <span class="badge bg-light text-dark">{{ $role->users->count() }} users</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted small mb-3">{{ $role->description }}</p>
                                    
                                    <h6 class="mb-2">Permissions ({{ count($role->permissions ?? []) }})</h6>
                                    <div class="permissions-list" style="max-height: 200px; overflow-y: auto;">
                                        @if($role->permissions && count($role->permissions) > 0)
                                            @foreach($role->permissions as $permission)
                                                <span class="badge bg-primary me-1 mb-1" style="font-size: 0.7rem;">
                                                    {{ ucwords(str_replace('_', ' ', $permission)) }}
                                                </span>
                                            @endforeach
                                        @else
                                            <span class="text-muted">No permissions assigned</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            @if($role->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-secondary">Inactive</span>
                                            @endif
                                        </div>
                                        <div class="btn-group" role="group">
                                            @if(auth()->user()->hasPermission('view_users'))
                                            <a href="{{ route('admin.roles.show', $role) }}" class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            @endif
                                            
                                            @if(auth()->user()->hasPermission('manage_user_roles'))
                                            <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Permissions Matrix -->
                    <div class="mt-5">
                        <h5>Permissions Matrix</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>Permission</th>
                                        @foreach($roles as $role)
                                            <th class="text-center">{{ $role->display_name }}</th>
                                        @endforeach
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($allPermissions as $permission)
                                    <tr>
                                        <td><strong>{{ ucwords(str_replace('_', ' ', $permission)) }}</strong></td>
                                        @foreach($roles as $role)
                                            <td class="text-center">
                                                @if(in_array($permission, $role->permissions ?? []))
                                                    <i class="bi bi-check-circle-fill text-success"></i>
                                                @else
                                                    <i class="bi bi-x-circle text-muted"></i>
                                                @endif
                                            </td>
                                        @endforeach
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.permissions-list .badge {
    font-size: 0.65rem;
}
</style>
@endsection
