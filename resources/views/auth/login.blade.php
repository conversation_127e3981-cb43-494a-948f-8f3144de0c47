@extends('layouts.app')

@section('content')
<style>
    body {
        background-color: #f8f9fa;
        background-image: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.4) 100%);
        background-size: cover;
        min-height: 100vh;
    }
    .login-container {
        margin-top: 5%;
    }
    .login-card {
        border: none;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(50, 50, 93, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
        transition: all 0.3s;
    }
    .login-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(50, 50, 93, 0.15), 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    .card-header-gold {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        padding: 2.5rem 1rem;
        text-align: center;
        border: none;
    }
    .logo-area {
        margin-bottom: 1.5rem;
    }
    .logo-icon {
        font-size: 3rem;
        color: white;
        background: rgba(255, 255, 255, 0.2);
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin: 0 auto;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    .input-group-custom {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    .input-group-text-custom {
        background-color: #fff;
        border: none;
        color: #FFA500;
        font-size: 1.2rem;
        padding-left: 15px;
    }
    .form-control-custom {
        border: none;
        padding: 12px 15px;
        font-size: 1rem;
        background-color: #fff;
    }
    .form-control-custom:focus {
        box-shadow: none;
        border: none;
    }
    .btn-gold {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        border: none;
        color: white;
        padding: 12px;
        font-weight: 600;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
        transition: all 0.3s;
    }
    .btn-gold:hover {
        transform: translateY(-2px);
        box-shadow: 0 7px 20px rgba(255, 165, 0, 0.4);
        color: white;
    }
    .card-footer-custom {
        background-color: #fff;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        color: #6c757d;
    }
    .remember-me-text {
        color: #6c757d;
    }
</style>

<div class="container login-container">
    <div class="row justify-content-center">
        <div class="col-md-5">
            
            <div class="card login-card">
                <div class="card-header-gold">
                    <div class="logo-area">
                        <div class="logo-icon">
                            <i class="bi bi-check2-circle"></i>
                        </div>
                    </div>
                    <h3 class="text-white fw-bold mb-1">{{ config('app.name', 'Vote Count') }}</h3>
                    <p class="text-white mb-0 opacity-75">{{ __('Sign in to continue to your account') }}</p>
                </div>

                <div class="card-body p-4 p-lg-5">
                    <form method="POST" action="{{ route('login') }}">
                        @csrf
                        
                        <div class="mb-4">
                            <label class="form-label fw-medium mb-2">{{ __('Phone Number') }}</label>
                            <div class="input-group input-group-custom">
                                <span class="input-group-text input-group-text-custom">
                                    <i class="bi bi-phone"></i>
                                </span>
                                <input id="phone_number" type="text" 
                                    class="form-control form-control-custom @error('phone_number') is-invalid @enderror" 
                                    name="phone_number" 
                                    value="{{ old('email') }}" 
                                    placeholder="{{ __('Enter your phone number') }}"
                                    required autocomplete="phone_number" autofocus>
                            </div>
                            @error('phone_number')
                                <span class="text-danger small mt-2 d-block" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label class="form-label fw-medium mb-2">{{ __('Password') }}</label>
                            <div class="input-group input-group-custom">
                                <span class="input-group-text input-group-text-custom">
                                    <i class="bi bi-lock"></i>
                                </span>
                                <input id="password" type="password" 
                                    class="form-control form-control-custom @error('password') is-invalid @enderror" 
                                    name="password" 
                                    placeholder="{{ __('Enter your password') }}"
                                    required autocomplete="current-password">
                            </div>
                            @error('password')
                                <span class="text-danger small mt-2 d-block" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>

                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}>
                            <label class="form-check-label remember-me-text" for="remember">
                                {{ __('Remember Me') }}
                            </label>
                        </div>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-gold py-3 fw-bold">
                                <i class="bi bi-box-arrow-in-right me-2"></i>{{ __('Sign In') }}
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="card-footer text-center py-3 card-footer-custom">
                    <div class="small">{{ __('Vote Count System') }} &copy; {{ date('Y') }}</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
