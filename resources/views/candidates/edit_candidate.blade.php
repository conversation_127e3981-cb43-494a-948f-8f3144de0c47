@extends('layouts.app')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">Edit Candidate</h5>
                </div>
                <div class="card-body">
                    @if(session('status'))
                        <div class="alert alert-danger">
                            {{ session('status') }}
                        </div>
                    @endif
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('candidates.update',$candidate->id) }}" method="post" enctype="multipart/form-data">
                        @csrf
                        @method('PATCH')
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" id="name" name="name" value="{{ old('name', $candidate->name) }}" class="form-control @error('name') is-invalid @enderror" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="picture" class="form-label">Picture (Optional)</label>
                            <div class="input-group mb-3">
                                <input type="file" class="form-control @error('picture') is-invalid @enderror" id="picture" name="picture">
                                <label class="input-group-text" for="picture">Upload</label>
                            </div>
                            @if($candidate->picture)
                                <div class="mt-2">
                                    <img src="{{ asset($candidate->picture) }}" alt="{{ $candidate->name }}" class="img-thumbnail" style="max-height: 100px;">
                                </div>
                            @endif
                            @error('picture')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="party_color" class="form-label">Party Color</label>
                            <div class="input-group">
                                <input type="color" class="form-control form-control-color" id="color_picker" value="{{ old('party_color', $candidate->party_color) }}" title="Choose party color">
                                <input type="text" id="party_color" name="party_color" value="{{ old('party_color', $candidate->party_color) }}" class="form-control @error('party_color') is-invalid @enderror">
                            </div>
                            @error('party_color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="party_name" class="form-label">Party Name</label>
                            <input type="text" id="party_name" name="party_name" value="{{ old('party_name', $candidate->party_name) }}" class="form-control @error('party_name') is-invalid @enderror">
                            @error('party_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('positions.show', $candidate->position_id) }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-warning text-white">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Update text input when color picker changes
    document.getElementById('color_picker').addEventListener('input', function() {
        document.getElementById('party_color').value = this.value;
    });
</script>
@endsection
