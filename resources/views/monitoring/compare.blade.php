@extends('layouts.app')

@section('content')
<div class="container-fluid px-3">
    <!-- Compact Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="header-card bg-gradient-primary text-white rounded-4 p-3 shadow-sm">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="header-icon-wrapper me-3">
                            <i class="bi bi-bar-chart-line fs-3"></i>
                        </div>
                        <div>
                            <h1 class="h5 mb-0 fw-bold">{{ $candidate->name }} Analysis</h1>
                            <small class="text-white-50">{{ $position->name }} • Detailed Comparison</small>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('monitoring.index') }}" class="btn btn-light btn-sm">
                            <i class="bi bi-arrow-left me-1"></i>Back
                        </a>
                        <button class="btn btn-outline-light btn-sm" onclick="window.print()">
                            <i class="bi bi-printer"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div class="row g-3">
        <!-- Left Column - Candidate Comparison -->
        <div class="col-lg-8">
            <!-- Head-to-Head Comparison -->
            <div class="comparison-panel bg-white rounded-3 shadow-sm mb-3">
                <div class="panel-header bg-light rounded-top-3 p-3 border-bottom">
                    <h6 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="bi bi-people-fill text-primary me-2"></i>
                        Head-to-Head Comparison
                    </h6>
                </div>
                <div class="panel-body p-3">
                    <div class="row g-3">
                        <!-- Your Candidate -->
                        <div class="col-md-6">
                            <div class="candidate-summary-card border rounded-3 p-3 h-100 {{ $gapInfo['is_leading'] ? 'border-success bg-success bg-opacity-5' : 'border-danger bg-danger bg-opacity-5' }}">
                                <div class="d-flex align-items-center mb-3">
                                    <!-- Avatar -->
                                    <div class="candidate-avatar me-3">
                                        @if(isset($candidate->picture))
                                            <img src="{{ asset('files/'.$candidate->picture) }}" alt="{{ $candidate->name }}" class="rounded-circle" width="50" height="50">
                                        @else
                                            @php
                                                $nameParts = explode(' ', $candidate->name);
                                                $initials = count($nameParts) >= 2 ? 
                                                    strtoupper(substr($nameParts[0], 0, 1) . substr($nameParts[count($nameParts)-1], 0, 1)) : 
                                                    strtoupper(substr($candidate->name, 0, 2));
                                                $bgColor = $candidate->party_color ?? ($gapInfo['is_leading'] ? '#28a745' : '#dc3545');
                                            @endphp
                                            <div class="rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="background: {{ $bgColor }}; color: white; font-weight: bold; width: 50px; height: 50px; font-size: 1.1rem;">
                                                {{ $initials }}
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0 fw-bold">{{ $candidate->name }}</h6>
                                        <small class="text-muted">Your Preferred Candidate</small>
                                        <div class="mt-1">
                                            <span class="badge bg-{{ $gapInfo['is_leading'] ? 'success' : 'danger' }}">
                                                @if($gapInfo['is_leading'])
                                                    1st Place
                                                @else
                                                    {{ $gapInfo['preferred_position'] ?? 'N/A' }}{{ $gapInfo['preferred_position'] == 2 ? 'nd' : ($gapInfo['preferred_position'] == 3 ? 'rd' : 'th') }} Place
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Vote Stats -->
                                <div class="vote-stats text-center">
                                    <div class="vote-number mb-2">
                                        <span class="fs-2 fw-bold text-{{ $gapInfo['is_leading'] ? 'success' : 'danger' }}">
                                            {{ number_format($gapInfo['preferred_votes']) }}
                                        </span>
                                    </div>
                                    <div class="vote-label text-muted small">Total Votes</div>
                                    @php
                                        $totalVotes = $gapInfo['preferred_votes'] + $gapInfo['competitor_votes'];
                                        $percentage = $totalVotes > 0 ? ($gapInfo['preferred_votes'] / $totalVotes) * 100 : 50;
                                    @endphp
                                    <div class="mt-2">
                                        <span class="badge bg-{{ $gapInfo['is_leading'] ? 'success' : 'danger' }} bg-opacity-75">
                                            {{ number_format($percentage, 1) }}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Competitor -->
                        @if($gapInfo['competitor'])
                        <div class="col-md-6">
                            <div class="candidate-summary-card border border-secondary bg-light rounded-3 p-3 h-100">
                                <div class="d-flex align-items-center mb-3">
                                    <!-- Avatar -->
                                    <div class="candidate-avatar me-3">
                                        @if(isset($gapInfo['competitor']->picture))
                                            <img src="{{ asset('files/'.$gapInfo['competitor']->picture) }}" alt="{{ $gapInfo['competitor']->name }}" class="rounded-circle" width="50" height="50">
                                        @else
                                            @php
                                                $nameParts = explode(' ', $gapInfo['competitor']->name);
                                                $initials = count($nameParts) >= 2 ? 
                                                    strtoupper(substr($nameParts[0], 0, 1) . substr($nameParts[count($nameParts)-1], 0, 1)) : 
                                                    strtoupper(substr($gapInfo['competitor']->name, 0, 2));
                                                $bgColor = $gapInfo['competitor']->party_color ?? '#6c757d';
                                            @endphp
                                            <div class="rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="background: {{ $bgColor }}; color: white; font-weight: bold; width: 50px; height: 50px; font-size: 1.1rem;">
                                                {{ $initials }}
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0 fw-bold">{{ $gapInfo['competitor']->name }}</h6>
                                        <small class="text-muted">
                                            @if($gapInfo['is_leading'])
                                                2nd Place Candidate
                                            @else
                                                Leading Candidate
                                            @endif
                                        </small>
                                        <div class="mt-1">
                                            <span class="badge bg-secondary">
                                                @if($gapInfo['is_leading'])
                                                    2nd Place
                                                @else
                                                    1st Place
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Vote Stats -->
                                <div class="vote-stats text-center">
                                    <div class="vote-number mb-2">
                                        <span class="fs-2 fw-bold text-secondary">
                                            {{ number_format($gapInfo['competitor_votes']) }}
                                        </span>
                                    </div>
                                    <div class="vote-label text-muted small">Total Votes</div>
                                    <div class="mt-2">
                                        <span class="badge bg-secondary bg-opacity-75">
                                            {{ number_format(100 - $percentage, 1) }}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                    
                    <!-- Vote Gap Analysis -->
                    @if($gapInfo['competitor'])
                    <div class="gap-analysis mt-4 p-3 rounded-3 {{ $gapInfo['is_leading'] ? 'bg-success bg-opacity-10' : 'bg-danger bg-opacity-10' }}">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <div class="gap-icon me-3">
                                        <i class="bi bi-{{ $gapInfo['is_leading'] ? 'arrow-up-circle-fill' : 'arrow-down-circle-fill' }} text-{{ $gapInfo['is_leading'] ? 'success' : 'danger' }} fs-3"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold text-{{ $gapInfo['is_leading'] ? 'success' : 'danger' }}">
                                            @if($gapInfo['is_leading'])
                                                Leading by {{ number_format($gapInfo['gap']) }} votes
                                            @else
                                                Behind by {{ number_format($gapInfo['gap']) }} votes
                                            @endif
                                        </h6>
                                        <small class="text-muted">
                                            @if($gapInfo['is_leading'])
                                                Ahead of 2nd place candidate
                                            @else
                                                Behind the leading candidate
                                            @endif
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="gap-percentage">
                                    @php
                                        $gapPercentage = $totalVotes > 0 ? ($gapInfo['gap'] / $totalVotes) * 100 : 0;
                                    @endphp
                                    <span class="fs-4 fw-bold text-{{ $gapInfo['is_leading'] ? 'success' : 'danger' }}">
                                        {{ number_format($gapPercentage, 1) }}%
                                    </span>
                                    <div class="small text-muted">Gap Percentage</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Progress Bar -->
                        <div class="mt-3">
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-{{ $gapInfo['is_leading'] ? 'success' : 'danger' }}" 
                                     style="width: {{ $percentage }}%" 
                                     aria-valuenow="{{ $percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                <div class="progress-bar bg-secondary" 
                                     style="width: {{ 100 - $percentage }}%" 
                                     aria-valuenow="{{ 100 - $percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <div class="d-flex justify-content-between mt-2">
                                <small class="text-{{ $gapInfo['is_leading'] ? 'success' : 'danger' }} fw-bold">
                                    {{ $candidate->name }}: {{ number_format($percentage, 1) }}%
                                </small>
                                <small class="text-secondary fw-bold">
                                    {{ $gapInfo['competitor']->name }}: {{ number_format(100 - $percentage, 1) }}%
                                </small>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Right Column - Quick Stats & Actions -->
        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="stats-panel bg-white rounded-3 shadow-sm mb-3">
                <div class="panel-header bg-light rounded-top-3 p-3 border-bottom">
                    <h6 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="bi bi-speedometer2 text-warning me-2"></i>
                        Quick Stats
                    </h6>
                </div>
                <div class="panel-body p-3">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="stat-box text-center p-2 bg-primary bg-opacity-10 rounded-2">
                                <div class="stat-value text-primary fw-bold">{{ $gapInfo['preferred_position'] ?? 'N/A' }}</div>
                                <div class="stat-label text-muted small">Current Position</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-box text-center p-2 bg-info bg-opacity-10 rounded-2">
                                <div class="stat-value text-info fw-bold">{{ $gapInfo['total_candidates'] ?? 'N/A' }}</div>
                                <div class="stat-label text-muted small">Total Candidates</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-box text-center p-2 bg-success bg-opacity-10 rounded-2">
                                <div class="stat-value text-success fw-bold">{{ number_format($percentage, 1) }}%</div>
                                <div class="stat-label text-muted small">Vote Share</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-box text-center p-2 bg-warning bg-opacity-10 rounded-2">
                                <div class="stat-value text-warning fw-bold">{{ number_format($gapInfo['gap']) }}</div>
                                <div class="stat-label text-muted small">Vote Gap</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Status -->
            @if($candidate->monitoring && $candidate->monitoring->vote_gap_alert_threshold)
            @php
                $alertThreshold = $candidate->monitoring->vote_gap_alert_threshold;
                $isAlertTriggered = $gapInfo['gap'] < $alertThreshold;
            @endphp
            <div class="alert-panel bg-white rounded-3 shadow-sm mb-3">
                <div class="panel-header bg-light rounded-top-3 p-3 border-bottom">
                    <h6 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="bi bi-bell{{ $isAlertTriggered ? '-fill' : '' }} text-{{ $isAlertTriggered ? 'danger' : 'success' }} me-2"></i>
                        Alert Status
                    </h6>
                </div>
                <div class="panel-body p-3">
                    <div class="alert alert-{{ $isAlertTriggered ? 'danger' : 'success' }} mb-0">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-{{ $isAlertTriggered ? 'exclamation-triangle-fill' : 'shield-check' }} me-2"></i>
                            <div>
                                <div class="fw-bold">
                                    @if($isAlertTriggered)
                                        Alert Triggered!
                                    @else
                                        All Clear
                                    @endif
                                </div>
                                <small>
                                    Threshold: {{ number_format($alertThreshold) }} votes<br>
                                    Current gap: {{ number_format($gapInfo['gap']) }} votes
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Actions -->
            <div class="actions-panel bg-white rounded-3 shadow-sm">
                <div class="panel-header bg-light rounded-top-3 p-3 border-bottom">
                    <h6 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="bi bi-gear text-secondary me-2"></i>
                        Actions
                    </h6>
                </div>
                <div class="panel-body p-3">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#settingsModal{{ $candidate->id }}">
                            <i class="bi bi-gear me-1"></i>Monitoring Settings
                        </button>
                        <a href="{{ route('monitoring.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-arrow-left me-1"></i>Back to Monitoring
                        </a>
                        <button class="btn btn-outline-info btn-sm" onclick="window.print()">
                            <i class="bi bi-printer me-1"></i>Print Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Settings Modal -->
<div class="modal fade" id="settingsModal{{ $candidate->id }}" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white border-0">
                <h5 class="modal-title fw-bold" id="settingsModalLabel">
                    <i class="bi bi-gear me-2"></i>Monitoring Settings
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <form action="{{ route('monitoring.settings', $candidate) }}" method="post">
                    @csrf

                    <div class="mb-3">
                        <label for="vote_gap_alert_threshold" class="form-label fw-bold">
                            <i class="bi bi-bell me-1"></i>Alert Threshold
                        </label>
                        <input type="number" name="vote_gap_alert_threshold" id="vote_gap_alert_threshold"
                               class="form-control" placeholder="Enter vote gap threshold"
                               value="{{ $candidate->monitoring->vote_gap_alert_threshold ?? '' }}">
                        <div class="form-text">Get alerted when vote gap falls below this number</div>
                    </div>

                    <div class="mb-3">
                        <label for="monitoring_notes" class="form-label fw-bold">
                            <i class="bi bi-sticky me-1"></i>Notes
                        </label>
                        <textarea name="monitoring_notes" id="monitoring_notes" class="form-control" rows="3"
                                  placeholder="Add monitoring notes...">{{ $candidate->monitoring->monitoring_notes ?? '' }}</textarea>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg me-1"></i>Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
    /* Modern Compact Compare Design */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .header-card {
        border: none;
        backdrop-filter: blur(10px);
    }

    .comparison-panel, .stats-panel, .alert-panel, .actions-panel {
        border: none;
        transition: all 0.3s ease;
    }

    .comparison-panel:hover, .stats-panel:hover, .alert-panel:hover, .actions-panel:hover {
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .candidate-summary-card {
        transition: all 0.3s ease;
        background: #fff;
    }

    .candidate-summary-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .panel-header {
        border-bottom: 1px solid #e9ecef;
    }

    .vote-stats {
        transition: all 0.3s ease;
    }

    .vote-number {
        transition: all 0.3s ease;
    }

    .candidate-summary-card:hover .vote-number {
        transform: scale(1.05);
    }

    .gap-analysis {
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.1);
    }

    .gap-analysis:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .stat-box {
        transition: all 0.3s ease;
    }

    .stat-box:hover {
        transform: translateY(-2px) scale(1.02);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
    }

    .badge {
        font-weight: 600;
    }

    .progress {
        border-radius: 4px;
        box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
    }

    .rounded-2 {
        border-radius: 8px !important;
    }

    .rounded-3 {
        border-radius: 12px !important;
    }

    .rounded-4 {
        border-radius: 16px !important;
    }

    .text-white-50 {
        color: rgba(255,255,255,0.7) !important;
    }

    .gap-icon {
        transition: all 0.3s ease;
    }

    .gap-analysis:hover .gap-icon {
        transform: scale(1.1);
    }

    .candidate-avatar {
        transition: all 0.3s ease;
    }

    .candidate-summary-card:hover .candidate-avatar {
        transform: scale(1.05);
    }

    /* Print styles */
    @media print {
        .btn, .header-card .btn {
            display: none !important;
        }

        .container-fluid {
            padding: 0 !important;
        }

        .shadow-sm {
            box-shadow: none !important;
        }

        .bg-gradient-primary {
            background: #667eea !important;
            -webkit-print-color-adjust: exact;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .gap-analysis .row {
            text-align: center;
        }

        .gap-analysis .col-md-4 {
            margin-top: 1rem;
        }

        .vote-number .fs-2 {
            font-size: 1.5rem !important;
        }
    }
</style>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add smooth animations for stat boxes
        const statBoxes = document.querySelectorAll('.stat-box');

        statBoxes.forEach(box => {
            box.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.02)';
            });

            box.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Add click animation for buttons
        const buttons = document.querySelectorAll('.btn');

        buttons.forEach(button => {
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Auto-refresh data every 30 seconds (optional)
        // setInterval(() => {
        //     window.location.reload();
        // }, 30000);
    });
</script>
@endpush
