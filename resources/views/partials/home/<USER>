<!-- Preferred Candidates Section -->
<div class="col-md-6">
    @if(count($preferredCandidates) > 0)
    <div class="section-header mb-3 d-flex justify-content-between align-items-center">
        <div>
            <i class="bi bi-star-fill section-icon text-warning"></i>
            <h2 class="section-title">Preferred Candidates</h2>
        </div>
        <div>
            <a href="{{ route('monitoring.index') }}" class="btn btn-sm btn-outline-primary">
                <i class="bi bi-gear"></i> Manage
            </a>
        </div>
    </div>
    
    <div class="row">
        @foreach($preferredCandidates as $candidate)
            @php
                $gapInfo = $monitoringData[$candidate->id] ?? null;
                if (!$gapInfo) continue;
                
                $isLeading = $gapInfo['is_leading'];
                $gap = $gapInfo['gap'];
                $competitor = $gapInfo['competitor'];
                $alertThreshold = $candidate->monitoring->vote_gap_alert_threshold;
                $isAlertTriggered = $alertThreshold && $gap < $alertThreshold;
                
                // Calculate percentage for visual representation
                $totalVotes = $gapInfo['preferred_votes'] + $gapInfo['competitor_votes'];
                $preferredPercentage = $totalVotes > 0 ? ($gapInfo['preferred_votes'] / $totalVotes) * 100 : 50;
                $gapPercentage = $totalVotes > 0 ? ($gap / $totalVotes) * 100 : 0;
                
                // Determine status colors and icons
                $statusColor = $isLeading ? 'success' : 'danger';
                $statusIcon = $isLeading ? 'bi-arrow-up-circle-fill' : 'bi-arrow-down-circle-fill';
                $alertIcon = $isAlertTriggered ? 'bi-exclamation-triangle-fill' : 'bi-shield-check';
                $alertColor = $isAlertTriggered ? 'danger' : 'success';
            @endphp
            <div class="col-12 mb-3">
                <div class="results-card results-card-compact shadow-sm border-{{ $statusColor }} h-100" style="min-height: 200px;">
                    <!-- Enhanced Header with Icon and Status Badge -->
                    <div class="results-header py-2 px-3 d-flex justify-content-between align-items-center bg-{{ $statusColor }} bg-opacity-10">
                        <div class="d-flex align-items-center">
                            <div class="me-2">
                                <i class="bi {{ $statusIcon }} text-{{ $statusColor }}" style="font-size: 1.5rem;"></i>
                            </div>
                            <div>
                                <h6 class="mb-0 fw-bold">{{ $candidate->name }}</h6>
                                <div class="d-flex align-items-center">
                                    <small class="text-muted">{{ $candidate->position->name }}</small>
                                    <span class="badge bg-{{ $statusColor }} ms-2 py-1">
                                        {{ $isLeading ? 'Leading' : 'Trailing' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <a href="{{ route('monitoring.compare', $candidate) }}" class="btn btn-sm btn-{{ $statusColor }}">
                            <i class="bi bi-bar-chart-line"></i> Details
                        </a>
                    </div>
                    
                    <!-- Enhanced Body with Vote Information -->
                    <div class="results-body p-3">
                        <!-- Vote Counts Row -->
                        <div class="row mb-3">
                            <div class="col-6">
                                <div class="d-flex flex-column">
                                    <div class="text-muted small">Your Candidate</div>
                                    <div class="d-flex align-items-baseline">
                                        <span class="fw-bold fs-5">{{ number_format($gapInfo['preferred_votes']) }}</span>
                                        <span class="ms-2 small text-muted">votes</span>
                                    </div>
                                </div>
                            </div>
                            @if($competitor)
                            <div class="col-6">
                                <div class="d-flex flex-column">
                                    <div class="text-muted small">{{ $competitor->name }}</div>
                                    <div class="d-flex align-items-baseline">
                                        <span class="fw-bold fs-5">{{ number_format($gapInfo['competitor_votes']) }}</span>
                                        <span class="ms-2 small text-muted">votes</span>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                        
                        <!-- Vote Gap Information -->
                        @if($competitor)
                        <div class="gap-analysis mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="bi {{ $alertIcon }} text-{{ $alertColor }} me-2"></i>
                                    <span class="text-{{ $statusColor }} fw-bold">{{ $isLeading ? 'Leading by' : 'Trailing by' }}</span>
                                </div>
                                <div class="fw-bold fs-5 text-{{ $isAlertTriggered ? 'danger' : $statusColor }}">
                                    {{ number_format($gap) }}
                                    <span class="ms-1 small">votes</span>
                                    @if($isAlertTriggered)
                                        <span class="badge bg-danger ms-1">ALERT</span>
                                    @endif
                                </div>
                            </div>
                            
                            <!-- Enhanced Progress Bar -->
                            <div class="mt-2 position-relative" style="height: 8px; background-color: #f0f0f0; border-radius: 4px; overflow: hidden;">
                                <!-- Candidate's Vote Bar -->
                                <div class="position-absolute top-0 start-0 bottom-0 bg-{{ $statusColor }}" 
                                    style="width: {{ $preferredPercentage }}%; border-radius: 4px;"></div>
                                
                                <!-- Gap Indicator -->
                                @if($isLeading)
                                <div class="position-absolute top-0 bottom-0" 
                                    style="left: {{ $preferredPercentage - min(10, $gapPercentage) }}%; width: {{ min(10, $gapPercentage) }}%; background: repeating-linear-gradient(45deg, rgba(255,255,255,0.5), rgba(255,255,255,0.5) 5px, transparent 5px, transparent 10px);"></div>
                                @else
                                <div class="position-absolute top-0 bottom-0" 
                                    style="left: {{ $preferredPercentage }}%; width: {{ min(10, $gapPercentage) }}%; background: repeating-linear-gradient(45deg, rgba(0,0,0,0.1), rgba(0,0,0,0.1) 5px, transparent 5px, transparent 10px);"></div>
                                @endif
                            </div>
                            
                            <!-- Threshold Indicator if configured -->
                            @if($alertThreshold)
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <small class="text-muted">Alert threshold: {{ number_format($alertThreshold) }} votes</small>
                                @if($isAlertTriggered)
                                <small class="text-danger">Threshold breached</small>
                                @endif
                            </div>
                            @endif
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    @else
    <div class="section-header mb-3">
        <i class="bi bi-star-fill section-icon text-warning"></i>
        <h2 class="section-title">Preferred Candidates</h2>
    </div>
    <div class="alert alert-info">
        <i class="bi bi-info-circle me-2"></i> No preferred candidates have been set up for monitoring.
        <a href="{{ route('monitoring.index') }}" class="alert-link">Set up monitoring</a> to track your preferred candidates.
    </div>
    @endif
</div>
