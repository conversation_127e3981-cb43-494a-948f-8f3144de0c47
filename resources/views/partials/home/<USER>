<!-- Charts Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="section-header d-flex align-items-center mb-3">
            <div class="section-icon-container me-2" style="background-color: rgba(13, 110, 253, 0.1); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                <i class="bi bi-graph-up-arrow text-primary" style="font-size: 1.2rem;"></i>
            </div>
            <h2 class="section-title">Results Analysis</h2>
        </div>
        
        <div class="row">
            <!-- Position Results -->
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Position Results</h5>
                            <select id="positionSelector" class="form-select form-select-sm" style="width: auto;">
                                @foreach ($positions as $position)
                                <option value="{{ $position->id }}">{{ $position->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="position-results-chart-container" style="height: 300px;">
                            <canvas id="positionResultsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Results by District -->
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Results by District</h5>
                            <select id="districtCandidateSelector" class="form-select form-select-sm" style="width: auto;">
                                @foreach ($positions as $position)
                                    @foreach ($position->candidates as $candidate)
                                    <option value="{{ $candidate->id }}" data-position="{{ $position->id }}">
                                        {{ $candidate->name }} ({{ $position->name }})
                                    </option>
                                    @endforeach
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="district-results-chart-container" style="height: 300px;">
                            <canvas id="districtResultsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
