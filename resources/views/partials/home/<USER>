<!-- Overall Results Section -->
<div class="col-md-6">
    <div class="section-header mb-2 d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <div class="section-icon-container me-2 pulse-animation" style="background: linear-gradient(135deg, #4a89dc, #5d9cec); width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 8px; box-shadow: 0 2px 5px rgba(74, 137, 220, 0.15);">
                <i class="bi bi-trophy-fill text-white" style="font-size: 0.9rem;"></i>
            </div>
            <h2 class="section-title mb-0 fw-bold" style="font-size: 1.2rem;">Overall Results</h2>
        </div>
        <span class="badge bg-primary px-2 py-1 rounded-pill" style="font-size: 0.7rem;">
            <i class="bi bi-award me-1"></i> Top 3
        </span>
    </div>
    
    @php
        // Get all candidates across all positions
        $allCandidates = collect();
        foreach ($positions as $position) {
            foreach ($position->candidates as $candidate) {
                $votes = $candidate->totalVotes();
                $allCandidates->push([
                    'candidate' => $candidate,
                    'position' => $position,
                    'votes' => $votes,
                    'percentage' => ($position->totalVotes() > 0) ? round(($votes/$position->totalVotes() * 100), 1) : 0
                ]);
            }
        }
        
        // Sort by votes (descending) and take top 3
        $topCandidates = $allCandidates->sortByDesc('votes')->take(3);
        $medals = ['🥇', '🥈', '🥉'];
        $colors = [
            ['gradient' => 'linear-gradient(135deg, #FFD700, #FFA500)', 'border' => 'border-warning', 'text' => 'text-warning', 'progress' => 'bg-warning'],
            ['gradient' => 'linear-gradient(135deg, #C0C0C0, #A9A9A9)', 'border' => 'border-secondary', 'text' => 'text-secondary', 'progress' => 'bg-secondary'],
            ['gradient' => 'linear-gradient(135deg, #CD7F32, #8B4513)', 'border' => 'border-dark', 'text' => 'text-dark', 'progress' => 'bg-dark']
        ];
    @endphp
    
    <div class="card border-0 shadow-sm mb-3">
        <div class="card-body p-0">
            <div class="list-group list-group-flush">
                @foreach ($topCandidates as $index => $item)
                <div class="list-group-item py-3 px-3 border-0 hover-item" style="transition: all 0.2s ease;">
                    <div class="d-flex align-items-start mb-2">
                        <div class="me-2" style="width: 28px; text-align: center;">
                             <span class="badge rounded-circle p-0" 
                                   style="background: {{ $colors[min($index, 2)]['gradient'] }}; width: 26px; height: 26px; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); font-size: 0.8rem;">
                                 {{ $loop->iteration }}
                             </span>
                        </div>
                        
                        <div class="d-flex flex-column flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                @php
                                    // Get candidate initials (up to 2 characters)
                                    $nameParts = explode(' ', $item['candidate']->name);
                                    $initials = '';
                                    if (count($nameParts) >= 2) {
                                        $initials = strtoupper(substr($nameParts[0], 0, 1) . substr($nameParts[count($nameParts)-1], 0, 1));
                                    } else {
                                        $initials = strtoupper(substr($item['candidate']->name, 0, 2));
                                    }
                                @endphp
                                <div class="rounded-circle d-flex align-items-center justify-content-center me-2" 
                                     style="width: 38px; height: 38px; background: {{ $colors[min($index, 2)]['gradient'] }}; color: white; font-weight: bold; font-size: 0.9rem;">
                                    {{ $initials }}
                                </div>
                                
                                <div>
                                    <div class="d-flex align-items-center">
                                        <span class="fw-bold me-1" style="font-size: 1rem;">{{ $item['candidate']->name }}</span>
                                        @if($loop->first)
                                            <span class="badge bg-success bg-opacity-10 text-success border border-success border-opacity-25 ms-1" style="font-size: 0.6rem; font-weight: 500;">
                                                <i class="bi bi-trophy-fill me-1"></i>Leading
                                            </span>
                                        @endif
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-award {{ $colors[min($index, 2)]['text'] }} me-1" style="font-size: 0.7rem;"></i>
                                        <small class="{{ $colors[min($index, 2)]['text'] }}" style="font-size: 0.8rem;">{{ $item['position']->name }}</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="progress flex-grow-1 me-2" style="height: 8px; border-radius: 4px; background-color: rgba(0,0,0,0.05);">
                                    <div class="progress-bar progress-bar-striped {{ $colors[min($index, 2)]['progress'] }}" role="progressbar" 
                                         style="width: {{ $item['percentage'] }}%; border-radius: 4px;" 
                                         aria-valuenow="{{ $item['percentage'] }}" aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                
                                <div class="text-end" style="min-width: 100px;">
                                    <div class="fw-bold" style="font-size: 1rem;">
                                        {{ number_format($item['votes']) }}
                                        <span class="badge rounded-pill ms-1" 
                                              style="background: {{ $colors[min($index, 2)]['gradient'] }}; font-size: 0.75rem;">
                                            {{ $item['percentage'] }}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    
    <style>
        .hover-item:hover {
            background-color: rgba(0,0,0,0.02);
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
    </style>
</div>
