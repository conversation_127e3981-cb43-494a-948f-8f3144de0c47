<!-- Left Column: Stats Cards -->
<div class="col-md-4 mb-4">
    <div class="card shadow-sm h-100">
        <div class="card-body p-4 d-flex flex-column">
            <h5 class="card-title text-center mb-4">Preferred Candidate</h5>
            <div class="flex-grow-1 d-flex align-items-center justify-content-center">
                <div style="position: relative; width: 100%; max-width: 300px; height: 300px; margin: 0 auto;">
                    <canvas id="preferredCandidateChart"></canvas>
                    <div class="position-absolute top-50 start-50 translate-middle text-center">
                        @php
                            // Find preferred candidate
                            $preferredCandidate = null;
                            $preferredVotes = 0;
                            $preferredPosition = null;
                            
                            // Find leading candidate
                            $leadingCandidate = null;
                            $leadingVotes = 0;
                            $leadingPosition = null;
                            
                            // Find both preferred and leading candidates
                            foreach($positions as $position) {
                                foreach($position->candidates as $candidate) {
                                    $candidateVotes = $candidate->totalVotes();
                                    
                                    // Check if this is a preferred candidate
                                    if ($candidate->isPreferred()) {
                                        $preferredCandidate = $candidate;
                                        $preferredVotes = $candidateVotes;
                                        $preferredPosition = $position;
                                    }
                                    
                                    // Check if this is the leading candidate overall
                                    if ($candidateVotes > $leadingVotes) {
                                        $leadingCandidate = $candidate;
                                        $leadingVotes = $candidateVotes;
                                        $leadingPosition = $position;
                                    }
                                }
                            }
                            
                            // Determine if preferred is also leading
                            $isPreferredLeading = ($preferredCandidate && $leadingCandidate && $preferredCandidate->id === $leadingCandidate->id);
                            
                            // Calculate percentage for preferred candidate
                            if ($isPreferredLeading) {
                                // If preferred is leading, calculate against total votes
                                $totalPositionVotes = 0;
                                if ($preferredPosition) {
                                    foreach($preferredPosition->candidates as $c) {
                                        $totalPositionVotes += $c->totalVotes();
                                    }
                                }
                                $preferredPercentage = ($totalPositionVotes > 0) ? round(($preferredVotes/$totalPositionVotes * 100), 1) : 0;
                            } else {
                                // If not leading, calculate against leading candidate
                                $totalVotes = $preferredVotes + $leadingVotes;
                                $preferredPercentage = ($totalVotes > 0) ? round(($preferredVotes/$totalVotes * 100), 1) : 0;
                            }
                            
                            // Get position (1st, 2nd, 3rd, etc.)
                            $candidateRank = 0;
                            if ($preferredCandidate && $preferredPosition) {
                                $sortedCandidates = $preferredPosition->candidates->sortByDesc(function($c) {
                                    return $c->totalVotes();
                                });
                                
                                foreach($sortedCandidates as $index => $c) {
                                    if ($c->id === $preferredCandidate->id) {
                                        $candidateRank = $index + 1;
                                        break;
                                    }
                                }
                            }
                            
                            // Format rank as 1st, 2nd, 3rd, etc.
                            $rankSuffix = 'th';
                            if ($candidateRank % 10 == 1 && $candidateRank % 100 != 11) {
                                $rankSuffix = 'st';
                            } elseif ($candidateRank % 10 == 2 && $candidateRank % 100 != 12) {
                                $rankSuffix = 'nd';
                            } elseif ($candidateRank % 10 == 3 && $candidateRank % 100 != 13) {
                                $rankSuffix = 'rd';
                            }
                            $rankFormatted = $candidateRank . $rankSuffix;
                        @endphp
                        
                        @if($preferredCandidate)
                        <div class="mb-2">
                            @if($isPreferredLeading)
                            <span class="badge bg-success px-2 py-1">
                                <i class="bi bi-trophy-fill me-1"></i> {{ $rankFormatted }} Position
                            </span>
                            @else
                            <span class="badge bg-primary bg-opacity-20 text-primary px-2 py-1">
                                <i class="bi bi-star-fill me-1"></i> {{ $rankFormatted }} Position
                            </span>
                            @endif
                        </div>
                        <div class="fw-bold" style="font-size: 1.8rem; line-height: 1.2;">{{ $preferredPercentage }}%</div>
                        <div class="text-muted small">vs {{ $isPreferredLeading ? 'others' : 'leading candidate' }}</div>
                        <div class="mt-2 fw-bold">{{ $preferredCandidate->name }}</div>
                        <div class="text-muted small">{{ $preferredPosition->name }} - {{ number_format($preferredVotes) }} votes</div>
                        @else
                        <div class="text-muted">No preferred candidate set</div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
