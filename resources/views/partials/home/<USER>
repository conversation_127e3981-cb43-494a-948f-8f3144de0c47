<!-- Polling Stations Map Component -->
<div class="card bg-white rounded shadow-sm mb-4">
    <div class="card-header bg-white py-3">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-geo-alt-fill text-primary me-2"></i>
                Polling Stations Map
            </h5>
            <div class="d-flex align-items-center gap-3">
                <div class="d-flex align-items-center">
                    <label for="mapCandidateSelector" class="form-label me-2 mb-0 small">Show Results For:</label>
                    <select id="mapCandidateSelector" class="form-select form-select-sm" style="width: 180px;">
                        <option value="">Select a candidate...</option>
                        @foreach ($positions as $position)
                            <optgroup label="{{ $position->name }}">
                                @foreach ($position->candidates as $candidate)
                                    <option value="{{ $candidate->id }}">{{ $candidate->name }}</option>
                                @endforeach
                            </optgroup>
                        @endforeach
                    </select>
                </div>
                <div class="d-flex align-items-center gap-1">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refreshMapBtn" title="Refresh Map">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="expandMapBtn" title="Expand Map">
                        <i class="bi bi-arrows-fullscreen"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0 position-relative" id="mapContainer">
        <div id="pollingStationsMap" style="height: 400px; width: 100%;"></div>
        <!-- Map loads instantly with embedded data - no loading indicator needed -->
    </div>
    <div class="card-footer bg-white py-2">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <span class="me-3"><i class="bi bi-circle-fill text-success me-1"></i> Won</span>
                <span><i class="bi bi-circle-fill text-danger me-1"></i> Lost</span>
            </div>
            <div class="small text-muted">Click on markers for details</div>
        </div>
    </div>
</div>

<!-- Fullscreen Map Overlay -->
<div id="fullscreenMapOverlay" class="fullscreen-map-overlay d-none">
    <div class="fullscreen-map-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0 text-white">
                <i class="bi bi-geo-alt-fill me-2"></i>
                Polling Stations Map - Fullscreen
            </h5>
            <div class="d-flex align-items-center gap-3">
                <div class="d-flex align-items-center">
                    <label for="fullscreenMapCandidateSelector" class="form-label me-2 mb-0 small text-white">Show Results For:</label>
                    <select id="fullscreenMapCandidateSelector" class="form-select form-select-sm" style="width: 180px;">
                        <option value="">Select a candidate...</option>
                        @foreach ($positions as $position)
                            <optgroup label="{{ $position->name }}">
                                @foreach ($position->candidates as $candidate)
                                    <option value="{{ $candidate->id }}">{{ $candidate->name }}</option>
                                @endforeach
                            </optgroup>
                        @endforeach
                    </select>
                </div>
                <div class="d-flex align-items-center gap-1">
                    <button type="button" class="btn btn-outline-light btn-sm" id="fullscreenRefreshMapBtn" title="Refresh Map">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light btn-sm" id="exitFullscreenBtn" title="Exit Fullscreen">
                        <i class="bi bi-fullscreen-exit"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="fullscreen-map-body">
        <div id="fullscreenPollingStationsMap" style="height: 100%; width: 100%;"></div>
    </div>
    <div class="fullscreen-map-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <span class="me-3 text-white"><i class="bi bi-circle-fill text-success me-1"></i> Won</span>
                <span class="me-3 text-white"><i class="bi bi-circle-fill text-danger me-1"></i> Lost</span>
                <span class="me-3 text-white"><i class="bi bi-circle-fill text-primary me-1"></i> All Stations</span>
            </div>
            <div class="text-white small">
                <i class="bi bi-info-circle me-1"></i>
                Click markers for details • Use mouse wheel to zoom • Drag to pan
            </div>
        </div>
    </div>
</div>

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />

<style>
    .marker-won {
        background-color: #28a745;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        border: 3px solid #fff;
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 10px;
    }

    .marker-lost {
        background-color: #dc3545;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        border: 3px solid #fff;
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 10px;
    }

    .leaflet-popup-content {
        margin: 8px 12px;
        line-height: 1.4;
    }

    .leaflet-popup-content-wrapper {
        border-radius: 8px;
    }
</style>

<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>

<script>
    // Wait for DOM and Leaflet to be fully loaded
    window.addEventListener('load', function() {
        console.log('Window loaded, initializing OpenStreetMap...');

        // Check if Leaflet is available
        if (typeof L === 'undefined') {
            console.error('Leaflet is not loaded');
            showFallbackMap();
            return;
        }

        try {
            console.log('Initializing Leaflet map...');

            // Initialize map centered on Uganda
            const map = L.map('pollingStationsMap').setView([1.373333, 32.290275], 7);

            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 18,
                minZoom: 5
            }).addTo(map);

            console.log('Map tiles loaded');

            // Add zoom control
            map.zoomControl.setPosition('topright');

            // Initialize variables
            let markers = L.markerClusterGroup({
                chunkedLoading: true,
                maxClusterRadius: 50
            });
            let pollingStations = [];
            let selectedCandidateId = null;

        // Get candidate selector (try both map-specific and general selector)
        const candidateSelector = document.getElementById('mapCandidateSelector') ||
                                 document.getElementById('candidateSelector');
        const loadingIndicator = document.getElementById('mapLoadingIndicator'); // May be null now

        console.log('Map elements initialized');

        // Add marker cluster group to map
        map.addLayer(markers);
        
        // Pre-calculated polling stations data with votes grouped by position
        @php
            $mapData = [];
            $candidatePositions = []; // Track which position each candidate belongs to

            try {
                // First, build a map of candidate_id => position_id for quick lookup
                foreach ($positions as $position) {
                    foreach ($position->candidates as $candidate) {
                        $candidatePositions[$candidate->id] = $position->id;
                    }
                }

                foreach ($all_polling_stations as $station) {
                    $stationData = [
                        'id' => $station->id,
                        'name' => $station->name,
                        'lat' => $station->latitude,
                        'lng' => $station->longitude,
                        'agent_name' => $station->agent ? $station->agent->user->name : null,
                        'total_votes' => 0,
                        'evidence_count' => $station->agent ? $station->agent->eveidences->count() : 0,
                        'votes' => [], // candidate_id => vote_count
                        'positions' => [] // position_id => [candidate_id => vote_count]
                    ];

                    // Calculate votes for each candidate at this station
                    foreach ($positions as $position) {
                        $stationData['positions'][$position->id] = [];

                        foreach ($position->candidates as $candidate) {
                            try {
                                $votes = \App\Models\Vote::join('agents', 'votes.agent_id', '=', 'agents.id')
                                    ->where('agents.polling_station_id', $station->id)
                                    ->where('votes.candidate_id', $candidate->id)
                                    ->sum('votes.number_of_votes') ?? 0;

                                $stationData['votes'][$candidate->id] = $votes;
                                $stationData['positions'][$position->id][$candidate->id] = $votes;
                            } catch (\Exception $e) {
                                $stationData['votes'][$candidate->id] = 0;
                                $stationData['positions'][$position->id][$candidate->id] = 0;
                            }
                        }
                    }

                    // Calculate total votes for this station
                    $stationData['total_votes'] = array_sum($stationData['votes']);

                    $mapData[] = $stationData;
                }

                // Debug information
                $totalStations = count($all_polling_stations);
                $stationsWithCoords = collect($all_polling_stations)->filter(function($station) {
                    return $station->latitude && $station->longitude;
                })->count();

            } catch (\Exception $e) {
                // If there's an error, create some dummy data with position structure
                $totalStations = 0;
                $stationsWithCoords = 0;
                $mapData = [
                    [
                        'id' => 1,
                        'name' => 'Sample Station 1',
                        'lat' => 0.3476,
                        'lng' => 32.5825,
                        'votes' => [1 => 150, 2 => 120, 3 => 80],
                        'positions' => [1 => [1 => 150, 2 => 120], 2 => [3 => 80]]
                    ],
                    [
                        'id' => 2,
                        'name' => 'Sample Station 2',
                        'lat' => 0.0512,
                        'lng' => 32.4414,
                        'votes' => [1 => 90, 2 => 110, 3 => 70],
                        'positions' => [1 => [1 => 90, 2 => 110], 2 => [3 => 70]]
                    ],
                    [
                        'id' => 3,
                        'name' => 'Sample Station 3',
                        'lat' => 0.4478,
                        'lng' => 33.2067,
                        'votes' => [1 => 130, 2 => 100, 3 => 110],
                        'positions' => [1 => [1 => 130, 2 => 100], 2 => [3 => 110]]
                    ]
                ];
                $candidatePositions = [1 => 1, 2 => 1, 3 => 2]; // candidate_id => position_id
            }
        @endphp

        // Embed candidate positions for JavaScript
        const candidatePositions = @json($candidatePositions);

        // Debug information from PHP
        console.log('=== MAP DATA DEBUG ===');
        console.log('Total stations from PHP:', {{ $totalStations ?? 0 }});
        console.log('Stations with coordinates from PHP:', {{ $stationsWithCoords ?? 0 }});

        // Function to get polling stations data (embedded from Laravel)
        function getPollingStationsData() {
            try {
                console.log('Loading embedded polling stations data...');

                // Get pre-calculated data from Laravel
                const stationsData = @json($mapData);
                console.log('Received stations data:', stationsData);
                console.log('Data type:', typeof stationsData);
                console.log('Data length:', stationsData ? stationsData.length : 'undefined');

                // Count stations with coordinates
                if (stationsData && Array.isArray(stationsData)) {
                    const withCoords = stationsData.filter(station => station.lat && station.lng);
                    console.log('Stations with coordinates in JS:', withCoords.length);
                    console.log('Sample station with coords:', withCoords[0]);

                    if (withCoords.length === 0) {
                        console.warn('No stations have coordinates!');
                        console.log('Sample station data:', stationsData[0]);
                    }
                }

                return stationsData || [];
            } catch (error) {
                console.error('Error processing polling stations:', error);
                // Fallback to dummy data if there's an error
                console.log('Using fallback dummy data');

                return [
                    { id: 1, name: 'Sample Station 1', lat: 0.3476, lng: 32.5825, votes: { 1: 150, 2: 120, 3: 80 } },
                    { id: 2, name: 'Sample Station 2', lat: 0.0512, lng: 32.4414, votes: { 1: 90, 2: 110, 3: 70 } },
                    { id: 3, name: 'Sample Station 3', lat: 0.4478, lng: 33.2067, votes: { 1: 130, 2: 100, 3: 110 } }
                ];
            }
        }
        
        // Function to determine if candidate won at a polling station (within their position)
        function didCandidateWin(station, candidateId) {
            if (!station.votes || !station.positions || !candidateId) return false;

            const candidateVotes = parseInt(station.votes[candidateId]) || 0;

            // If candidate has 0 votes, they didn't win
            if (candidateVotes === 0) return false;

            // Get the position this candidate belongs to
            const positionId = candidatePositions[candidateId];
            if (!positionId || !station.positions[positionId]) return false;

            // Only compare against candidates in the same position
            const positionVotes = station.positions[positionId];

            let maxVotes = 0;
            let winnersCount = 0;

            // Find the maximum votes within this position only
            Object.entries(positionVotes).forEach(([id, votes]) => {
                const numVotes = parseInt(votes) || 0;
                if (numVotes > maxVotes) {
                    maxVotes = numVotes;
                    winnersCount = 1;
                } else if (numVotes === maxVotes && numVotes > 0) {
                    winnersCount++;
                }
            });

            // Candidate won if they have the maximum votes in their position
            return candidateVotes === maxVotes && maxVotes > 0;
        }

        // Function to create a custom marker icon
        function createMarkerIcon(won) {
            return L.divIcon({
                className: won ? 'marker-won' : 'marker-lost',
                html: won ? '✓' : '✗',
                iconSize: [20, 20],
                iconAnchor: [10, 10],
                popupAnchor: [0, -10]
            });
        }
        
        // Function to create a popup for a marker
        function createPopupContent(station, candidateId) {
            const candidateVotes = parseInt(station.votes[candidateId]) || 0;

            // Get the position this candidate belongs to
            const positionId = candidatePositions[candidateId];
            if (!positionId || !station.positions || !station.positions[positionId]) {
                return `<div class="popup-content"><h6>${station.name}</h6><p>Error: Position data not found</p></div>`;
            }

            // Only calculate within the same position
            const positionVotes = station.positions[positionId];
            let positionTotalVotes = 0;
            let maxVotes = 0;
            let winningCandidateId = null;
            let winnersCount = 0;

            // Calculate total votes and find winning candidate(s) within this position only
            Object.entries(positionVotes).forEach(([id, votes]) => {
                const numVotes = parseInt(votes) || 0; // Ensure it's a number
                positionTotalVotes += numVotes;
                if (numVotes > maxVotes) {
                    maxVotes = numVotes;
                    winningCandidateId = id;
                    winnersCount = 1;
                } else if (numVotes === maxVotes && numVotes > 0) {
                    winnersCount++;
                }
            });

            const percentage = positionTotalVotes > 0 ? ((candidateVotes / positionTotalVotes) * 100).toFixed(1) : 0;
            const won = didCandidateWin(station, candidateId);

            // Determine status with more detail (within position context)
            let status, statusClass;
            if (positionTotalVotes === 0) {
                status = '<span class="badge bg-secondary">No Votes</span>';
                statusClass = 'bg-secondary';
            } else if (candidateVotes === 0) {
                status = '<span class="badge bg-danger">Lost (0 votes)</span>';
                statusClass = 'bg-danger';
            } else if (won) {
                if (winnersCount > 1) {
                    status = '<span class="badge bg-warning text-dark">Tied for 1st</span>';
                    statusClass = 'bg-warning';
                } else {
                    status = '<span class="badge bg-success">Won</span>';
                    statusClass = 'bg-success';
                }
            } else {
                const rank = Object.values(positionVotes).filter(v => parseInt(v) > candidateVotes).length + 1;
                const suffix = rank === 1 ? 'st' : rank === 2 ? 'nd' : rank === 3 ? 'rd' : 'th';
                status = `<span class="badge bg-danger">Lost (${rank}${suffix} place)</span>`;
                statusClass = 'bg-danger';
            }

            return `
                <div class="popup-content">
                    <h6 class="mb-1">${station.name}</h6>
                    <div class="mb-2">${status}</div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>Candidate Votes:</span>
                        <strong>${candidateVotes.toLocaleString()}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>Position Total:</span>
                        <strong>${positionTotalVotes.toLocaleString()}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Percentage:</span>
                        <strong>${percentage}%</strong>
                    </div>
                    ${positionTotalVotes > 0 ? `
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar ${statusClass}"
                             role="progressbar"
                             style="width: ${percentage}%"
                             aria-valuenow="${percentage}"
                             aria-valuemin="0"
                             aria-valuemax="100">
                        </div>
                    </div>
                    ` : ''}
                    ${maxVotes > 0 ? `
                    <div class="mt-2 small text-muted">
                        Highest in position: ${maxVotes.toLocaleString()}
                    </div>
                    ` : ''}
                </div>
            `;
        }
        
        // Function to update markers based on selected candidate
        function updateMarkers() {
            console.log('=== UPDATING MARKERS ===');
            console.log('Total polling stations:', pollingStations.length);

            // Clear existing markers
            markers.clearLayers();

            let markersAdded = 0;
            let stationsWithCoords = 0;
            let stationsWithoutCoords = 0;

            // Create markers for each polling station
            pollingStations.forEach((station, index) => {
                if (station.lat && station.lng) {
                    stationsWithCoords++;

                    try {
                        let icon, popupContent;

                        if (selectedCandidateId) {
                            // Show win/loss status for selected candidate
                            const won = didCandidateWin(station, selectedCandidateId);
                            icon = createMarkerIcon(won);
                            popupContent = createPopupContent(station, selectedCandidateId);
                        } else {
                            // Show default marker for all stations
                            icon = createDefaultMarkerIcon();
                            popupContent = createDefaultPopupContent(station);
                        }

                        const marker = L.marker([parseFloat(station.lat), parseFloat(station.lng)], {
                            icon: icon
                        });

                        marker.bindPopup(popupContent);

                        // Add marker to cluster group
                        markers.addLayer(marker);
                        markersAdded++;

                        if (index < 3) {
                            console.log(`Sample marker ${index + 1}:`, {
                                name: station.name,
                                lat: station.lat,
                                lng: station.lng
                            });
                        }
                    } catch (error) {
                        console.error('Error creating marker for station:', station.name, error);
                    }
                } else {
                    stationsWithoutCoords++;
                    if (index < 3) {
                        console.log(`Station without coords ${index + 1}:`, {
                            name: station.name,
                            lat: station.lat,
                            lng: station.lng
                        });
                    }
                }
            });

            console.log(`Stations with coordinates: ${stationsWithCoords}`);
            console.log(`Stations without coordinates: ${stationsWithoutCoords}`);
            console.log(`Markers successfully added: ${markersAdded}`);
            console.log(`Markers in cluster group: ${markers.getLayers().length}`);
        }

        // Function to create default marker icon (neutral)
        function createDefaultMarkerIcon() {
            return L.divIcon({
                className: 'custom-marker-icon',
                html: `<div style="
                    background-color: #007bff;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    border: 2px solid white;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                "></div>`,
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            });
        }

        // Function to create default popup content
        function createDefaultPopupContent(station) {
            return `
                <div class="map-popup">
                    <h6 class="mb-2">${station.name}</h6>
                    <p class="mb-1"><strong>Agent:</strong> ${station.agent_name || 'Not assigned'}</p>
                    <p class="mb-1"><strong>Total Votes:</strong> ${station.total_votes || 0}</p>
                    <p class="mb-1"><strong>Evidence:</strong> ${station.evidence_count || 0} files</p>
                    <p class="mb-0"><strong>Coordinates:</strong> ${parseFloat(station.lat).toFixed(4)}, ${parseFloat(station.lng).toFixed(4)}</p>
                </div>
            `;
        }

        // Initialize map and load data
        console.log('Map initialized, loading polling stations...');

        // Load data synchronously since it's embedded
        pollingStations = getPollingStationsData();
        console.log('Polling stations loaded:', pollingStations.length, 'stations');
        if (pollingStations.length > 0) {
            console.log('Sample station data:', pollingStations[0]);
        }

        // Show all markers by default
        updateMarkers();
        console.log('Map ready for use - all polling stations displayed');
        console.log('Stations with coordinates:', pollingStations.filter(s => s.lat && s.lng).length);

        // Additional verification
        if (pollingStations.length > 300) {
            console.log('✅ SUCCESS: Map has access to all stations!');
        } else {
            console.log('⚠️ WARNING: Map may not have all stations. Expected 350+, got:', pollingStations.length);
        }

        // Handle candidate selection
        if (candidateSelector) {
            candidateSelector.addEventListener('change', function() {
                selectedCandidateId = this.value;

                if (selectedCandidateId) {
                    console.log('Map candidate selected:', selectedCandidateId);
                } else {
                    console.log('No map candidate selected, showing all stations');
                }

                // Always update markers (will show all stations if no candidate selected)
                updateMarkers();
            });
        } else {
            console.log('Map candidate selector not found - showing all stations by default');
        }

        // Also listen to the home page candidate selector if it exists and is different
        const homeCandidateSelector = document.getElementById('candidateSelector');
        if (homeCandidateSelector && homeCandidateSelector !== candidateSelector) {
            homeCandidateSelector.addEventListener('change', function() {
                // Sync the map with home page candidate selection
                selectedCandidateId = this.value;

                if (selectedCandidateId) {
                    console.log('Home page candidate selected for map:', selectedCandidateId);
                } else {
                    console.log('No home page candidate selected, showing all stations on map');
                }

                // Update map markers
                updateMarkers();
            });
            console.log('Listening to home page candidate selector for map updates');
        }

        // Make functions globally accessible for external updates
        window.updateMapMarkers = updateMarkers;
        window.refreshMapData = function() {
            console.log('Refreshing map data...');
            // Reload polling stations data (in a real implementation, this would fetch fresh data)
            pollingStations = getPollingStationsData();
            updateMarkers();
            console.log('Map data refreshed');
        };


        } catch (error) {
            console.error('Error initializing map:', error);
            showFallbackMap();
        }

        // Fallback map function when Leaflet is not available
        function showFallbackMap() {
            const mapContainer = document.getElementById('pollingStationsMap');
            const candidateSelector = document.getElementById('mapCandidateSelector');

            console.log('Showing fallback map');

            // Create a simple list-based fallback
            mapContainer.innerHTML = `
                <div class="p-4 text-center bg-light">
                    <i class="bi bi-geo-alt display-4 text-muted mb-3"></i>
                    <h5>Polling Stations List</h5>
                    <p class="text-muted">Interactive map is not available. Showing polling stations as a list.</p>
                    <div id="stationsList" class="mt-4">
                        <p class="text-muted">Select a candidate to view results by station.</p>
                    </div>
                </div>
            `;

            // Handle candidate selection for fallback
            candidateSelector.addEventListener('change', function() {
                const selectedCandidateId = this.value;
                const stationsList = document.getElementById('stationsList');

                if (!selectedCandidateId) {
                    stationsList.innerHTML = '<p class="text-muted">Select a candidate to view results by station.</p>';
                    return;
                }

                // Get polling stations data
                const pollingStationsData = getPollingStationsData();

                let listHTML = '<div class="row">';
                pollingStationsData.forEach(station => {
                    // Calculate votes for this candidate at this station (position-based)
                    const candidateVotes = parseInt(station.votes[selectedCandidateId]) || 0;

                    // Get the position this candidate belongs to
                    const positionId = candidatePositions[selectedCandidateId];
                    if (!positionId || !station.positions || !station.positions[positionId]) {
                        return; // Skip if position data not found
                    }

                    // Only calculate within the same position
                    const positionVotes = station.positions[positionId];
                    const positionTotalVotes = Object.values(positionVotes).reduce((sum, v) => sum + parseInt(v), 0);
                    const percentage = positionTotalVotes > 0 ? ((candidateVotes / positionTotalVotes) * 100).toFixed(1) : 0;
                    const won = didCandidateWin(station, selectedCandidateId);

                    // Calculate additional details within position
                    let maxVotes = 0;
                    let winnersCount = 0;
                    Object.values(positionVotes).forEach(votes => {
                        const numVotes = parseInt(votes) || 0;
                        if (numVotes > maxVotes) {
                            maxVotes = numVotes;
                            winnersCount = 1;
                        } else if (numVotes === maxVotes && numVotes > 0) {
                            winnersCount++;
                        }
                    });

                    // Determine status and styling
                    let status, borderClass, badgeClass;
                    if (positionTotalVotes === 0) {
                        status = 'No Votes';
                        borderClass = 'secondary';
                        badgeClass = 'secondary';
                    } else if (candidateVotes === 0) {
                        status = 'Lost (0 votes)';
                        borderClass = 'danger';
                        badgeClass = 'danger';
                    } else if (won) {
                        if (winnersCount > 1) {
                            status = 'Tied for 1st';
                            borderClass = 'warning';
                            badgeClass = 'warning text-dark';
                        } else {
                            status = 'Won';
                            borderClass = 'success';
                            badgeClass = 'success';
                        }
                    } else {
                        const rank = Object.values(positionVotes).filter(v => parseInt(v) > candidateVotes).length + 1;
                        const suffix = rank === 1 ? 'st' : rank === 2 ? 'nd' : rank === 3 ? 'rd' : 'th';
                        status = `Lost (${rank}${suffix} place)`;
                        borderClass = 'danger';
                        badgeClass = 'danger';
                    }

                    listHTML += `
                        <div class="col-md-6 mb-3">
                            <div class="card border-${borderClass}">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">${station.name}</h6>
                                            <small class="text-muted">Station ID: ${station.id}</small>
                                        </div>
                                        <span class="badge bg-${badgeClass}">${status}</span>
                                    </div>
                                    <div class="mt-2">
                                        <div class="d-flex justify-content-between">
                                            <span>Candidate Votes:</span>
                                            <strong>${candidateVotes.toLocaleString()}</strong>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>Position Total:</span>
                                            <strong>${positionTotalVotes.toLocaleString()}</strong>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>Percentage:</span>
                                            <strong>${percentage}%</strong>
                                        </div>
                                        ${positionTotalVotes > 0 ? `
                                        <div class="progress mt-2" style="height: 6px;">
                                            <div class="progress-bar bg-${borderClass}"
                                                 style="width: ${percentage}%"></div>
                                        </div>
                                        ` : ''}
                                        ${maxVotes > 0 ? `
                                        <div class="mt-1 small text-muted">
                                            Highest in position: ${maxVotes.toLocaleString()}
                                        </div>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                listHTML += '</div>';

                stationsList.innerHTML = listHTML;
            });
        }

        // Fullscreen Map Functionality
        let fullscreenMap = null;
        let fullscreenMarkers = null;
        let isFullscreen = false;

        function initializeFullscreenMap() {
            console.log('=== INITIALIZING FULLSCREEN MAP ===');

            if (fullscreenMap) {
                console.log('Removing existing fullscreen map');
                fullscreenMap.remove();
            }

            try {
                // Check if Leaflet is available
                if (typeof L === 'undefined') {
                    console.error('Leaflet not available for fullscreen map');
                    return;
                }

                // Check if container exists
                const container = document.getElementById('fullscreenPollingStationsMap');
                if (!container) {
                    console.error('Fullscreen map container not found');
                    return;
                }

                console.log('Initializing fullscreen map...');

                // Initialize fullscreen map
                fullscreenMap = L.map('fullscreenPollingStationsMap').setView([1.373333, 32.290275], 7);

                // Add OpenStreetMap tiles
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                    maxZoom: 18,
                    minZoom: 5
                }).addTo(fullscreenMap);

                console.log('Fullscreen map tiles added');

                // Initialize marker cluster group
                fullscreenMarkers = L.markerClusterGroup({
                    chunkedLoading: true,
                    maxClusterRadius: 50
                });
                fullscreenMap.addLayer(fullscreenMarkers);

                console.log('Fullscreen marker cluster group added');
                console.log('Available polling stations:', pollingStations.length);

                // Copy markers from main map
                updateFullscreenMarkers();

                console.log('Fullscreen map initialization complete');

            } catch (error) {
                console.error('Error initializing fullscreen map:', error);
            }
        }

        function updateFullscreenMarkers() {
            if (!fullscreenMarkers) {
                console.error('Fullscreen markers not initialized');
                return;
            }

            console.log('Updating fullscreen markers...');
            console.log('Polling stations available:', pollingStations.length);

            fullscreenMarkers.clearLayers();

            // Get selected candidate from fullscreen selector
            const fullscreenSelector = document.getElementById('fullscreenMapCandidateSelector');
            const fullscreenSelectedCandidateId = fullscreenSelector ? fullscreenSelector.value : null;

            let markersAdded = 0;

            pollingStations.forEach((station, index) => {
                if (station.lat && station.lng) {
                    try {
                        let icon, popupContent;

                        if (fullscreenSelectedCandidateId) {
                            // Check if functions exist before calling them
                            if (typeof didCandidateWin === 'function' && typeof createMarkerIcon === 'function' && typeof createPopupContent === 'function') {
                                const won = didCandidateWin(station, fullscreenSelectedCandidateId);
                                icon = createMarkerIcon(won);
                                popupContent = createPopupContent(station, fullscreenSelectedCandidateId);
                            } else {
                                // Fallback to default if functions not available
                                icon = createDefaultMarkerIcon();
                                popupContent = createDefaultPopupContent(station);
                            }
                        } else {
                            icon = createDefaultMarkerIcon();
                            popupContent = createDefaultPopupContent(station);
                        }

                        const marker = L.marker([parseFloat(station.lat), parseFloat(station.lng)], {
                            icon: icon
                        });

                        marker.bindPopup(popupContent);
                        fullscreenMarkers.addLayer(marker);
                        markersAdded++;

                        if (index < 3) {
                            console.log(`Fullscreen marker ${index + 1}:`, {
                                name: station.name,
                                lat: station.lat,
                                lng: station.lng
                            });
                        }
                    } catch (error) {
                        console.error('Error creating fullscreen marker for station:', station.name, error);
                    }
                }
            });

            console.log(`Fullscreen map updated with ${markersAdded} markers`);
            console.log(`Markers in fullscreen cluster group: ${fullscreenMarkers.getLayers().length}`);

            // Fit bounds to show all markers if we have any
            if (markersAdded > 0) {
                setTimeout(() => {
                    try {
                        fullscreenMap.fitBounds(fullscreenMarkers.getBounds(), { padding: [20, 20] });
                    } catch (error) {
                        console.log('Could not fit bounds, using default view');
                        fullscreenMap.setView([1.373333, 32.290275], 7);
                    }
                }, 100);
            }
        }

        // Event Listeners for Fullscreen
        const expandMapBtn = document.getElementById('expandMapBtn');
        const exitFullscreenBtn = document.getElementById('exitFullscreenBtn');
        const refreshMapBtn = document.getElementById('refreshMapBtn');
        const fullscreenRefreshMapBtn = document.getElementById('fullscreenRefreshMapBtn');
        const fullscreenMapCandidateSelector = document.getElementById('fullscreenMapCandidateSelector');

        if (expandMapBtn) {
            expandMapBtn.addEventListener('click', function() {
                console.log('=== EXPANDING MAP ===');
                console.log('Polling stations available for fullscreen:', pollingStations.length);

                const overlay = document.getElementById('fullscreenMapOverlay');
                if (!overlay) {
                    console.error('Fullscreen overlay not found');
                    return;
                }

                overlay.classList.remove('d-none');
                isFullscreen = true;

                // Initialize fullscreen map after a short delay to ensure DOM is ready
                setTimeout(() => {
                    console.log('Initializing fullscreen map...');
                    initializeFullscreenMap();

                    // Sync candidate selection
                    const mainSelector = document.getElementById('mapCandidateSelector');
                    const fullscreenSelector = document.getElementById('fullscreenMapCandidateSelector');
                    if (mainSelector && fullscreenSelector) {
                        fullscreenSelector.value = mainSelector.value;
                        console.log('Synced candidate selection:', mainSelector.value);
                    }

                    // Force a resize event to ensure map renders properly
                    setTimeout(() => {
                        if (fullscreenMap) {
                            fullscreenMap.invalidateSize();
                            console.log('Fullscreen map size invalidated');
                        }
                    }, 200);
                }, 150);
            });
        }

        if (exitFullscreenBtn) {
            exitFullscreenBtn.addEventListener('click', function() {
                const overlay = document.getElementById('fullscreenMapOverlay');
                overlay.classList.add('d-none');
                isFullscreen = false;

                if (fullscreenMap) {
                    fullscreenMap.remove();
                    fullscreenMap = null;
                    fullscreenMarkers = null;
                }
            });
        }

        // Sync candidate selectors
        if (fullscreenMapCandidateSelector) {
            fullscreenMapCandidateSelector.addEventListener('change', function() {
                updateFullscreenMarkers();
            });
        }

        if (fullscreenRefreshMapBtn) {
            fullscreenRefreshMapBtn.addEventListener('click', function() {
                if (isFullscreen) {
                    updateFullscreenMarkers();
                }
            });
        }

        // ESC key to exit fullscreen
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isFullscreen) {
                if (exitFullscreenBtn) {
                    exitFullscreenBtn.click();
                }
            }
        });

        // Refresh map button for main map
        if (refreshMapBtn) {
            refreshMapBtn.addEventListener('click', function() {
                updateMarkers();
            });
        }

        // Add test function for debugging fullscreen map
        window.testFullscreenMap = function() {
            console.log('=== FULLSCREEN MAP TEST ===');
            console.log('Is fullscreen:', isFullscreen);
            console.log('Fullscreen map exists:', !!fullscreenMap);
            console.log('Fullscreen markers exists:', !!fullscreenMarkers);
            console.log('Polling stations available:', pollingStations.length);
            console.log('Stations with coordinates:', pollingStations.filter(s => s.lat && s.lng).length);

            if (fullscreenMarkers) {
                console.log('Markers in fullscreen cluster:', fullscreenMarkers.getLayers().length);
            }

            return {
                isFullscreen,
                hasMap: !!fullscreenMap,
                hasMarkers: !!fullscreenMarkers,
                stationCount: pollingStations.length,
                markerCount: fullscreenMarkers ? fullscreenMarkers.getLayers().length : 0
            };
        };

    });
</script>

<style>
.fullscreen-map-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.95);
    z-index: 9999;
    display: flex;
    flex-direction: column;
}

.fullscreen-map-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.fullscreen-map-body {
    flex: 1;
    position: relative;
}

.fullscreen-map-footer {
    background: rgba(0, 0, 0, 0.8);
    padding: 0.75rem 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fullscreen-map-header .d-flex {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start !important;
    }

    .fullscreen-map-header .d-flex > div {
        width: 100%;
        justify-content: space-between;
    }

    .fullscreen-map-footer .d-flex {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start !important;
    }
}

/* Animation for smooth transitions */
.fullscreen-map-overlay {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Button hover effects */
#expandMapBtn:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

#exitFullscreenBtn:hover {
    background-color: rgba(220, 53, 69, 0.2) !important;
    border-color: #dc3545 !important;
}

/* Ensure map containers have proper styling */
#pollingStationsMap, #fullscreenPollingStationsMap {
    border-radius: 8px;
}

#fullscreenPollingStationsMap {
    border-radius: 0;
}
</style>
