<!-- Polling Stations Section -->
<div class="row mt-4 mb-3">
    <div class="col-12">
        <div class="section-header d-flex align-items-center mb-3">
            <div class="section-icon-container me-2" style="background-color: rgba(13, 110, 253, 0.1); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                <i class="bi bi-building text-primary" style="font-size: 1.2rem;"></i>
            </div>
            <h2 class="section-title">Polling Stations</h2>
        </div>
        
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3 flex-wrap">
                    <!-- Search and Filter Controls -->
                    <div class="d-flex flex-wrap gap-2 mb-2 mb-md-0">
                        <div class="input-group input-group-sm" style="width: 250px;">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-search"></i>
                            </span>
                            <input type="text" id="stationSearchInput" class="form-control border-start-0" placeholder="Search stations...">
                        </div>
                        
                        <div class="input-group input-group-sm" style="width: 200px;">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-funnel"></i>
                            </span>
                            <select id="statusFilter" class="form-select border-start-0">
                                <option value="all">All Statuses</option>
                                <option value="has-results">Has Results</option>
                                <option value="no-results">No Results</option>
                                <option value="has-evidence">Has Evidence</option>
                                <option value="no-evidence">No Evidence</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Station Stats -->
                    <div class="d-flex flex-wrap gap-3">
                        <div class="station-stat">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-primary bg-opacity-10 text-primary">
                                    <i class="bi bi-building"></i>
                                </div>
                                <div>
                                    <div class="stat-value">{{ $polling_stations->count() }}</div>
                                    <div class="stat-label">Total</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="station-stat">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-success bg-opacity-10 text-success">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                                <div>
                                    <div class="stat-value">{{ $stationsWithResults }}</div>
                                    <div class="stat-label">With Results</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="station-stat">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info bg-opacity-10 text-info">
                                    <i class="bi bi-card-image"></i>
                                </div>
                                <div>
                                    <div class="stat-value">{{ $stationsWithEvidence }}</div>
                                    <div class="stat-label">With Evidence</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive polling-stations-table-container">
                    <table class="table table-hover table-striped align-middle" id="pollingStationsTable">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 50px;" class="text-center">#</th>
                                <th>Station Name</th>
                                <th class="text-center">Agent</th>
                                <th class="text-center">Results</th>
                                <th class="text-center">Evidence</th>
                                <th class="text-center">Last Updated</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($polling_stations as $index => $polling_station)
                            @php
                                // Check if agent has submitted any votes and get the latest submission time
                                $latestVote = null;
                                $hasSubmittedResults = false;
                                $evidenceCount = 0;
                                
                                if ($polling_station->agent) {
                                    // Check for submitted votes
                                    $votes = $polling_station->agent->votes;
                                    if ($votes && $votes->count() > 0) {
                                        $hasSubmittedResults = true;
                                        $latestVote = $votes->sortByDesc('updated_at')->first();
                                    }
                                    
                                    // Check for evidence
                                    $evidenceCount = $polling_station->agent->evidence->count();
                                }
                                
                                // Format the last updated time
                                $lastUpdated = $latestVote ? $latestVote->updated_at->diffForHumans() : '--';
                                
                                // Determine row status class
                                $rowStatusClass = '';
                                if ($hasSubmittedResults) {
                                    $rowStatusClass = 'table-success';
                                } elseif ($polling_station->agent) {
                                    $rowStatusClass = 'table-warning';
                                }
                            @endphp
                            <tr class="station-row" 
                                data-has-evidence="{{ $evidenceCount > 0 ? 'yes' : 'no' }}"
                                data-has-results="{{ $hasSubmittedResults ? 'yes' : 'no' }}"
                                data-station-id="{{ $polling_station->id }}"
                                data-station-name="{{ $polling_station->name }}">
                                <td class="text-center">{{ $index + 1 }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="station-icon me-2 rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 32px; height: 32px; background-color: rgba(13, 110, 253, 0.1); color: #0d6efd; font-size: 0.9rem;">
                                            <i class="bi bi-building"></i>
                                        </div>
                                        <div>
                                            <div class="station-name fw-medium">{{ $polling_station->name }}</div>
                                            <div class="station-details small text-muted">
                                                <span class="me-2">
                                                    <i class="bi bi-geo-alt me-1"></i>{{ $polling_station->location ?: 'No location' }}
                                                </span>
                                                <span>
                                                    <i class="bi bi-people me-1"></i>{{ number_format($polling_station->registered_voters) }} voters
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">
                                    @if($polling_station->agent)
                                        <span class="badge bg-success">
                                            <i class="bi bi-person-check me-1"></i>Assigned
                                        </span>
                                    @else
                                        <span class="badge bg-secondary">
                                            <i class="bi bi-person-x me-1"></i>Not Assigned
                                        </span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if($hasSubmittedResults)
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>Submitted
                                        </span>
                                    @else
                                        <span class="badge bg-secondary">
                                            <i class="bi bi-x-circle me-1"></i>Missing
                                        </span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if($polling_station->agent && $evidenceCount > 0)
                                        <span class="badge bg-info">{{ $evidenceCount }}</span>
                                    @else
                                        <span class="text-muted">--</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if($latestVote)
                                        <span class="text-muted small" title="{{ $latestVote->updated_at }}">
                                            <i class="bi bi-clock me-1"></i>{{ $lastUpdated }}
                                        </span>
                                    @else
                                        <span class="text-muted">--</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    <button class="btn btn-sm btn-outline-primary view-station-btn" data-station-id="{{ $polling_station->id }}">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination Controls -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="showing-entries">
                        Showing <span id="showing-start">1</span> to <span id="showing-end">{{ min(10, $polling_stations->count()) }}</span> of {{ $polling_stations->count() }} entries
                    </div>
                    
                    <nav aria-label="Polling stations pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled" id="prev-page">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item" id="next-page">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
