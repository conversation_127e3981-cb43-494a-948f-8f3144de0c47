@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Page Header -->
    <div class="section-header d-flex align-items-center mb-4">
        <div class="section-icon-container me-3" style="background-color: rgba(220, 53, 69, 0.1); width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
            <i class="bi bi-file-earmark-text text-danger" style="font-size: 1.5rem;"></i>
        </div>
        <div>
            <h2 class="section-title mb-0 fw-bold">Spoiled Votes Details</h2>
            <p class="text-muted mb-0">Detailed information about this spoiled votes record</p>
        </div>
        <div class="ms-auto">
            <a href="{{ route('spoiled_votes.edit', $spoiledVote->id) }}" class="btn btn-primary me-2">
                <i class="bi bi-pencil me-1"></i> Edit Record
            </a>
            <a href="{{ route('spoiled_votes.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Main Info Card -->
            <div class="card bg-white border-0 shadow-sm rounded-3 mb-4">
                <div class="card-header bg-white py-3 border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0 fw-bold">
                            <i class="bi bi-clipboard-data me-2 text-primary"></i>
                            Spoiled Votes Record #{{ $spoiledVote->id }}
                        </h5>
                        <span class="badge bg-danger px-3 py-2 rounded-pill">
                            {{ number_format($spoiledVote->number_of_votes) }} Spoiled Votes
                        </span>
                    </div>
                </div>

                <div class="card-body p-4">
                    <!-- Summary Row -->
                    <div class="row mb-4">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3 p-2 bg-primary bg-opacity-10 rounded-circle">
                                    <i class="bi bi-building text-primary"></i>
                                </div>
                                <h5 class="mb-0 fw-bold">Polling Station</h5>
                            </div>
                            <div class="ps-2 border-start border-3 border-primary">
                                <div class="mb-2">
                                    <span class="text-muted">Name:</span>
                                    <span class="fw-bold ms-2">{{ $spoiledVote->pollingStation->name }}</span>
                                </div>
                                <div class="mb-2">
                                    <span class="text-muted">Location:</span>
                                    <span class="ms-2">{{ $spoiledVote->pollingStation->location }}</span>
                                </div>
                                <div>
                                    <span class="text-muted">Code:</span>
                                    <span class="ms-2 badge bg-light text-dark">{{ $spoiledVote->pollingStation->code }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3 p-2 bg-warning bg-opacity-10 rounded-circle">
                                    <i class="bi bi-award text-warning"></i>
                                </div>
                                <h5 class="mb-0 fw-bold">Position</h5>
                            </div>
                            <div class="ps-2 border-start border-3 border-warning">
                                <div class="mb-2">
                                    <span class="text-muted">Name:</span>
                                    <span class="fw-bold ms-2">{{ $spoiledVote->position->name }}</span>
                                </div>
                                <div>
                                    <span class="text-muted">Candidates:</span>
                                    <span class="ms-2 badge bg-light text-dark">{{ $spoiledVote->position->candidates->count() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <!-- Second Row -->
                    <div class="row mb-4">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3 p-2 bg-info bg-opacity-10 rounded-circle">
                                    <i class="bi bi-person text-info"></i>
                                </div>
                                <h5 class="mb-0 fw-bold">Agent Information</h5>
                            </div>
                            <div class="ps-2 border-start border-3 border-info">
                                <div class="mb-2">
                                    <span class="text-muted">Name:</span>
                                    <span class="fw-bold ms-2">{{ $spoiledVote->agent->user->name }}</span>
                                </div>
                                <div>
                                    <span class="text-muted">Phone:</span>
                                    <span class="ms-2">
                                        <i class="bi bi-telephone me-1"></i>
                                        {{ $spoiledVote->agent->user->phone_number }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3 p-2 bg-danger bg-opacity-10 rounded-circle">
                                    <i class="bi bi-x-circle text-danger"></i>
                                </div>
                                <h5 class="mb-0 fw-bold">Spoiled Votes Data</h5>
                            </div>
                            <div class="ps-2 border-start border-3 border-danger">
                                <div class="mb-2">
                                    <span class="text-muted">Count:</span>
                                    <span class="fw-bold ms-2">
                                        <span class="badge bg-danger px-3 py-2">{{ number_format($spoiledVote->number_of_votes) }}</span>
                                    </span>
                                </div>
                                <div class="mb-2">
                                    <span class="text-muted">Recorded:</span>
                                    <span class="ms-2">
                                        <i class="bi bi-calendar-event me-1"></i>
                                        {{ $spoiledVote->created_at->format('M d, Y g:i A') }}
                                    </span>
                                </div>
                                <div>
                                    <span class="text-muted">Updated:</span>
                                    <span class="ms-2">
                                        <i class="bi bi-clock-history me-1"></i>
                                        {{ $spoiledVote->updated_at->format('M d, Y g:i A') }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Remarks Section -->
                    @if($spoiledVote->remarks)
                    <div class="mt-4 pt-4 border-top">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3 p-2 bg-secondary bg-opacity-10 rounded-circle">
                                <i class="bi bi-chat-left-quote text-secondary"></i>
                            </div>
                            <h5 class="mb-0 fw-bold">Remarks</h5>
                        </div>
                        <div class="p-3 bg-light rounded-3 border-start border-4 border-secondary">
                            <p class="mb-0">{{ $spoiledVote->remarks }}</p>
                        </div>
                    </div>
                    @endif
                </div>
                
                <!-- Card Footer -->
                <div class="card-footer bg-white py-3 d-flex justify-content-between">
                    <div>
                        <form action="{{ route('spoiled_votes.destroy', $spoiledVote->id) }}" method="POST" class="d-inline delete-form">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to delete this record? This action cannot be undone.')">
                                <i class="bi bi-trash me-1"></i> Delete Record
                            </button>
                        </form>
                    </div>
                    <div>
                        <a href="{{ route('spoiled_votes.edit', $spoiledVote->id) }}" class="btn btn-primary">
                            <i class="bi bi-pencil me-1"></i> Edit Record
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Related Records Card -->
            <div class="card bg-white border-0 shadow-sm rounded-3">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-link-45deg me-2 text-primary"></i>
                        Related Information
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-building me-2 text-primary"></i>
                                <h6 class="mb-0">Polling Station</h6>
                            </div>
                            <a href="#" class="btn btn-outline-primary btn-sm d-block mt-2">
                                <i class="bi bi-eye me-1"></i> View Station Details
                            </a>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-award me-2 text-warning"></i>
                                <h6 class="mb-0">Position</h6>
                            </div>
                            <a href="#" class="btn btn-outline-warning btn-sm d-block mt-2">
                                <i class="bi bi-eye me-1"></i> View Position Results
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
