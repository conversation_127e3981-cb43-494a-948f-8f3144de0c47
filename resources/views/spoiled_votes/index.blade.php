@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Page Header -->
    <div class="section-header d-flex align-items-center mb-4">
        <div class="section-icon-container me-3" style="background-color: rgba(220, 53, 69, 0.1); width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
            <i class="bi bi-x-circle text-danger" style="font-size: 1.5rem;"></i>
        </div>
        <div>
            <h2 class="section-title mb-0 fw-bold">Spoiled Votes Records</h2>
            <p class="text-muted mb-0">Manage and track spoiled votes across all polling stations</p>
        </div>
        <div class="ms-auto">
            <a href="{{ route('spoiled_votes.create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i> Record Spoiled Votes
            </a>
        </div>
    </div>

    <!-- Alerts -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i> {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card bg-white border-0 shadow-sm rounded-3 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle p-3 bg-danger bg-opacity-10 me-3">
                            <i class="bi bi-file-earmark-x text-danger"></i>
                        </div>
                        <div>
                            <h3 class="mb-0 fw-bold">{{ number_format($spoiledVotes->sum('number_of_votes')) }}</h3>
                            <p class="text-muted mb-0">Total Spoiled Votes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card bg-white border-0 shadow-sm rounded-3 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle p-3 bg-primary bg-opacity-10 me-3">
                            <i class="bi bi-geo-alt text-primary"></i>
                        </div>
                        <div>
                            <h3 class="mb-0 fw-bold">{{ $spoiledVotes->pluck('polling_station_id')->unique()->count() }}</h3>
                            <p class="text-muted mb-0">Affected Stations</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card bg-white border-0 shadow-sm rounded-3 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle p-3 bg-warning bg-opacity-10 me-3">
                            <i class="bi bi-award text-warning"></i>
                        </div>
                        <div>
                            <h3 class="mb-0 fw-bold">{{ $spoiledVotes->pluck('position_id')->unique()->count() }}</h3>
                            <p class="text-muted mb-0">Affected Positions</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="card bg-white border-0 shadow-sm rounded-3">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="mb-0 fw-bold">All Spoiled Votes Records</h5>
            <div class="btn-group">
                <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left me-1"></i> Back to Dashboard
                </a>
            </div>
        </div>

        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="py-3">Polling Station</th>
                            <th class="py-3">Position</th>
                            <th class="py-3">Agent</th>
                            <th class="py-3 text-center">Spoiled Votes</th>
                            <th class="py-3">Recorded At</th>
                            <th class="py-3 text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($spoiledVotes as $spoiledVote)
                            <tr>
                                <td class="align-middle">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-building me-2 text-muted"></i>
                                        {{ $spoiledVote->pollingStation->name }}
                                    </div>
                                </td>
                                <td class="align-middle">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-award me-2 text-warning"></i>
                                        {{ $spoiledVote->position->name }}
                                    </div>
                                </td>
                                <td class="align-middle">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-person me-2 text-primary"></i>
                                        {{ $spoiledVote->agent->user->name }}
                                    </div>
                                </td>
                                <td class="align-middle text-center">
                                    <span class="badge bg-danger px-3 py-2 rounded-pill">
                                        {{ number_format($spoiledVote->number_of_votes) }}
                                    </span>
                                </td>
                                <td class="align-middle">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-calendar-event me-2 text-muted"></i>
                                        <span>{{ $spoiledVote->created_at->format('M d, Y') }}</span>
                                        <span class="ms-2 badge bg-light text-dark">{{ $spoiledVote->created_at->format('H:i') }}</span>
                                    </div>
                                </td>
                                <td class="align-middle text-end">
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('spoiled_votes.show', $spoiledVote->id) }}" class="btn btn-outline-info btn-sm" title="View Details">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{{ route('spoiled_votes.edit', $spoiledVote->id) }}" class="btn btn-outline-primary btn-sm" title="Edit Record">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <form action="{{ route('spoiled_votes.destroy', $spoiledVote->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this record?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger btn-sm" title="Delete Record">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="bi bi-x-circle text-muted mb-3" style="font-size: 2rem;"></i>
                                        <h5>No spoiled votes recorded yet</h5>
                                        <p class="text-muted">Start recording spoiled votes to see them listed here</p>
                                        <a href="{{ route('spoiled_votes.create') }}" class="btn btn-primary">
                                            <i class="bi bi-plus-circle me-1"></i> Record First Spoiled Vote
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
            </div>
        </div>
    </div>
</div>
@endsection
