@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Page Header -->
    <div class="section-header d-flex align-items-center mb-4">
        <div class="section-icon-container me-3" style="background-color: rgba(220, 53, 69, 0.1); width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
            <i class="bi bi-pencil-square text-danger" style="font-size: 1.5rem;"></i>
        </div>
        <div>
            <h2 class="section-title mb-0 fw-bold">Edit Spoiled Votes Record</h2>
            <p class="text-muted mb-0">Update information about spoiled ballots</p>
        </div>
        <div class="ms-auto">
            <a href="{{ route('spoiled_votes.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Form Card -->
            <div class="card bg-white border-0 shadow-sm rounded-3">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-clipboard-check me-2 text-primary"></i>
                        Edit Spoiled Votes Details
                    </h5>
                    <div class="small text-muted mt-1">
                        <i class="bi bi-clock me-1"></i> Last updated: {{ $spoiledVote->updated_at->diffForHumans() }}
                    </div>
                </div>

                <div class="card-body p-4">
                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <strong>Please correct the following errors:</strong>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            <ul class="list-unstyled mb-0 mt-2">
                                @foreach ($errors->all() as $error)
                                    <li><i class="bi bi-dot me-1"></i> {{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('spoiled_votes.update', $spoiledVote->id) }}">
                        @csrf
                        @method('PUT')

                        <div class="row mb-4">
                            <!-- Polling Station Selection -->
                            <div class="col-md-6 mb-3 mb-md-0">
                                <label for="polling_station_id" class="form-label fw-bold">
                                    <i class="bi bi-building me-1 text-primary"></i>
                                    Polling Station <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-geo-alt"></i>
                                    </span>
                                    <select class="form-select @error('polling_station_id') is-invalid @enderror" id="polling_station_id" name="polling_station_id" required>
                                        <option value="">Select Polling Station</option>
                                        @foreach($pollingStations as $station)
                                            <option value="{{ $station->id }}" {{ (old('polling_station_id', $spoiledVote->polling_station_id) == $station->id) ? 'selected' : '' }}>
                                                {{ $station->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('polling_station_id')
                                    <div class="invalid-feedback d-block">
                                        <i class="bi bi-exclamation-circle me-1"></i> {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <!-- Position Selection -->
                            <div class="col-md-6">
                                <label for="position_id" class="form-label fw-bold">
                                    <i class="bi bi-award me-1 text-warning"></i>
                                    Position <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-person-badge"></i>
                                    </span>
                                    <select class="form-select @error('position_id') is-invalid @enderror" id="position_id" name="position_id" required>
                                        <option value="">Select Position</option>
                                        @foreach($positions as $position)
                                            <option value="{{ $position->id }}" {{ (old('position_id', $spoiledVote->position_id) == $position->id) ? 'selected' : '' }}>
                                                {{ $position->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('position_id')
                                    <div class="invalid-feedback d-block">
                                        <i class="bi bi-exclamation-circle me-1"></i> {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>

                        <!-- Number of Spoiled Votes -->
                        <div class="mb-4">
                            <label for="number_of_votes" class="form-label fw-bold">
                                <i class="bi bi-hash me-1 text-danger"></i>
                                Number of Spoiled Votes <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-123"></i>
                                </span>
                                <input type="number" class="form-control @error('number_of_votes') is-invalid @enderror" id="number_of_votes" name="number_of_votes" value="{{ old('number_of_votes', $spoiledVote->number_of_votes) }}" min="0" required>
                            </div>
                            @error('number_of_votes')
                                <div class="invalid-feedback d-block">
                                    <i class="bi bi-exclamation-circle me-1"></i> {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Remarks -->
                        <div class="mb-4">
                            <label for="remarks" class="form-label fw-bold">
                                <i class="bi bi-chat-left-text me-1 text-muted"></i>
                                Remarks (Optional)
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-pencil"></i>
                                </span>
                                <textarea class="form-control @error('remarks') is-invalid @enderror" id="remarks" name="remarks" rows="3" placeholder="Describe the reason for spoiled votes or any other relevant information">{{ old('remarks', $spoiledVote->remarks) }}</textarea>
                            </div>
                            @error('remarks')
                                <div class="invalid-feedback d-block">
                                    <i class="bi bi-exclamation-circle me-1"></i> {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Record Info -->
                        <div class="alert alert-light mb-4">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-info-circle text-primary me-2"></i>
                                <h6 class="mb-0">Record Information</h6>
                            </div>
                            <div class="row small text-muted">
                                <div class="col-md-6">
                                    <div class="mb-1"><strong>Created:</strong> {{ $spoiledVote->created_at->format('M d, Y g:i A') }}</div>
                                    <div><strong>Created by:</strong> {{ $spoiledVote->agent->user->name ?? 'Unknown' }}</div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-1"><strong>Last Updated:</strong> {{ $spoiledVote->updated_at->format('M d, Y g:i A') }}</div>
                                    <div><strong>Record ID:</strong> #{{ $spoiledVote->id }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="border-top pt-4 mt-4 d-flex justify-content-between">
                            <a href="{{ route('spoiled_votes.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-1"></i> Update Spoiled Votes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
