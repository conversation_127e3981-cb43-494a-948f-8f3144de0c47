@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Page Header -->
    <div class="section-header d-flex align-items-center mb-4">
        <div class="section-icon-container me-3" style="background-color: rgba(220, 53, 69, 0.1); width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
            <i class="bi bi-x-circle text-danger" style="font-size: 1.5rem;"></i>
        </div>
        <div>
            <h2 class="section-title mb-0 fw-bold">Record Spoiled Votes</h2>
            <p class="text-muted mb-0">Document spoiled ballots for accurate election monitoring</p>
        </div>
        <div class="ms-auto">
            <a href="{{ route('spoiled_votes.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Form Card -->
            <div class="card bg-white border-0 shadow-sm rounded-3">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-clipboard-check me-2 text-primary"></i>
                        Spoiled Votes Details
                    </h5>
                </div>

                <div class="card-body p-4">
                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <strong>Please correct the following errors:</strong>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            <ul class="list-unstyled mb-0 mt-2">
                                @foreach ($errors->all() as $error)
                                    <li><i class="bi bi-dot me-1"></i> {{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('spoiled_votes.store') }}">
                        @csrf

                        <div class="row mb-4">
                            <!-- Polling Station Selection -->
                            <div class="col-md-6 mb-3 mb-md-0">
                                <label for="polling_station_id" class="form-label fw-bold">
                                    <i class="bi bi-building me-1 text-primary"></i>
                                    Polling Station <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-geo-alt"></i>
                                    </span>
                                    <select class="form-select @error('polling_station_id') is-invalid @enderror" id="polling_station_id" name="polling_station_id" required {{ Auth::user()->user_type == 'agent' && $preselectedStation ? 'disabled' : '' }}>
                                        <option value="">Select Polling Station</option>
                                        @foreach($pollingStations as $station)
                                            <option value="{{ $station->id }}" {{ (old('polling_station_id') == $station->id || (isset($preselectedStation) && $preselectedStation == $station->id)) ? 'selected' : '' }}>
                                                {{ $station->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                @if(Auth::user()->user_type == 'agent' && $preselectedStation)
                                    <input type="hidden" name="polling_station_id" value="{{ $preselectedStation }}">
                                    <div class="form-text text-muted">
                                        <i class="bi bi-info-circle me-1"></i> As an agent, you can only record spoiled votes for your assigned polling station.
                                    </div>
                                @endif
                                @error('polling_station_id')
                                    <div class="invalid-feedback d-block">
                                        <i class="bi bi-exclamation-circle me-1"></i> {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <!-- Position Selection -->
                            <div class="col-md-6">
                                <label for="position_id" class="form-label fw-bold">
                                    <i class="bi bi-award me-1 text-warning"></i>
                                    Position <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-person-badge"></i>
                                    </span>
                                    <select class="form-select @error('position_id') is-invalid @enderror" id="position_id" name="position_id" required>
                                        <option value="">Select Position</option>
                                        @foreach($positions as $position)
                                            <option value="{{ $position->id }}" {{ old('position_id') == $position->id ? 'selected' : '' }}>
                                                {{ $position->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('position_id')
                                    <div class="invalid-feedback d-block">
                                        <i class="bi bi-exclamation-circle me-1"></i> {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-4">
                            <!-- Number of Spoiled Votes -->
                            <div class="col-md-6 mb-3 mb-md-0">
                                <label for="number_of_votes" class="form-label fw-bold">
                                    <i class="bi bi-hash me-1 text-danger"></i>
                                    Number of Spoiled Votes <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-123"></i>
                                    </span>
                                    <input type="number" class="form-control @error('number_of_votes') is-invalid @enderror" id="number_of_votes" name="number_of_votes" value="{{ old('number_of_votes', 0) }}" min="0" required>
                                </div>
                                @error('number_of_votes')
                                    <div class="invalid-feedback d-block">
                                        <i class="bi bi-exclamation-circle me-1"></i> {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <!-- Agent Selection (Admin Only) -->
                            @if(Auth::user()->user_type != 'agent')
                                <div class="col-md-6">
                                    <label for="agent_id" class="form-label fw-bold">
                                        <i class="bi bi-person me-1 text-primary"></i>
                                        Agent <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="bi bi-person-vcard"></i>
                                        </span>
                                        <select class="form-select @error('agent_id') is-invalid @enderror" id="agent_id" name="agent_id" required>
                                            <option value="">Select Agent</option>
                                            @foreach(App\Models\Agent::with('user')->get() as $agent)
                                                <option value="{{ $agent->id }}" {{ old('agent_id') == $agent->id ? 'selected' : '' }}>
                                                    {{ $agent->user->name }} ({{ $agent->user->phone_number }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @error('agent_id')
                                        <div class="invalid-feedback d-block">
                                            <i class="bi bi-exclamation-circle me-1"></i> {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            @endif
                        </div>

                        <!-- Remarks -->
                        <div class="mb-4">
                            <label for="remarks" class="form-label fw-bold">
                                <i class="bi bi-chat-left-text me-1 text-muted"></i>
                                Remarks (Optional)
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-pencil"></i>
                                </span>
                                <textarea class="form-control @error('remarks') is-invalid @enderror" id="remarks" name="remarks" rows="3" placeholder="Describe the reason for spoiled votes or any other relevant information">{{ old('remarks') }}</textarea>
                            </div>
                            @error('remarks')
                                <div class="invalid-feedback d-block">
                                    <i class="bi bi-exclamation-circle me-1"></i> {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Form Actions -->
                        <div class="border-top pt-4 mt-4 d-flex justify-content-between">
                            <a href="{{ route('spoiled_votes.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-1"></i> Save Spoiled Votes
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card bg-light border-0 mt-4">
                <div class="card-body p-3">
                    <h6 class="card-title mb-2">
                        <i class="bi bi-info-circle me-2 text-primary"></i>
                        About Spoiled Votes
                    </h6>
                    <p class="card-text small mb-0">Spoiled votes are ballots that cannot be counted due to improper marking, damage, or other issues that make the voter's intent unclear. Recording these accurately helps maintain election integrity.</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
