## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: Authentication
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/login
    metadata:
      groupName: Authentication
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Log In  User'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      phone_number:
        name: phone_number
        description: 'The phone number of the user'
        required: true
        example: consequatur
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'The password of the user'
        required: true
        example: 'O[2UZ5ij-e/dl4m{o,'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      phone_number: consequatur
      password: 'O[2UZ5ij-e/dl4m{o,'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
                "status": "success",
                "message": "Successfully loged in",
                "authorization": {
                    "token": "1|VvxyULU6as0bluOJRBtbNsJLjIcC3G2lVDnvdfUL7e46d8f2",
                    "token_type": "Bearer"
                },
                "user": {
                    "id": 1,
                    "name": "Kawalya Paul",
                    "phone_number": "0785297660",
                    "email_verified_at": null,
                    "user_type": "admin",
                    "created_at": "2025-07-04T06:40:37.000000Z",
                    "updated_at": "2025-07-04T06:40:37.000000Z"
                }
            }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/logout
    metadata:
      groupName: Authentication
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Agent Logout'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/change_password
    metadata:
      groupName: Authentication
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Change password'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      old_password:
        name: old_password
        description: ''
        required: true
        example: consequatur
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: ''
        required: true
        example: 'O[2UZ5ij-e/dl4m{o,'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password_confirmation:
        name: password_confirmation
        description: ''
        required: true
        example: consequatur
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      old_password: consequatur
      password: 'O[2UZ5ij-e/dl4m{o,'
      password_confirmation: consequatur
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
               "status": "success",
                "message": "Password changes"
            }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/user
    metadata:
      groupName: Authentication
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logged In User'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
                "status": "success",
                "message": "Logged in user",
                "user": {
                    "id": 1,
                    "name": "Kawalya Paul",
                    "phone_number": "0785297660",
                    "email_verified_at": null,
                    "user_type": "admin",
                    "created_at": "2025-07-04T06:40:37.000000Z",
                    "updated_at": "2025-07-04T06:40:37.000000Z"
                }
            }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
