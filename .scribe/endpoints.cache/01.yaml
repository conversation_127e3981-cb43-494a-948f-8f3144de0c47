## Autogenerated by <PERSON><PERSON><PERSON>. DO NOT MODIFY.

name: Votes
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/load_candidates
    metadata:
      groupName: Votes
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: Candidates
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "status": "success",
            "message": "Candidates",
            "postions": [
                {
                    "id": 1,
                    "created_at": "2025-07-04T07:03:33.000000Z",
                    "updated_at": "2025-07-04T07:03:44.000000Z",
                    "name": "Member of Parliment - Bukonzo West.",
                    "candidates": [
                        {
                            "id": 1,
                            "created_at": "2025-07-04T07:35:44.000000Z",
                            "updated_at": "2025-07-04T07:42:40.000000Z",
                            "name": "Mutsuba Bahatii",
                            "position_id": 1,
                            "picture": "1751614544lnqdhIObCYHF.jpeg",
                            "party_color": "yellow",
                            "party_name": "NRM"
                        },
                        {
                            "id": 2,
                            "created_at": "2025-07-04T07:45:02.000000Z",
                            "updated_at": "2025-07-04T07:45:02.000000Z",
                            "name": "Bwambale James",
                            "position_id": 1,
                            "picture": "1751615102tBQMdUMePHTq.jpg",
                            "party_color": "yellow",
                            "party_name": "NRM"
                        }
                    ]
                }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/record_votes
    metadata:
      groupName: Votes
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Record Vote'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      candidate_id:
        name: candidate_id
        description: ''
        required: true
        example: 17
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      number_of_votes:
        name: number_of_votes
        description: ''
        required: true
        example: 17
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      latitude:
        name: latitude
        description: ''
        required: false
        example: 11613.31890586
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      longitude:
        name: longitude
        description: ''
        required: false
        example: 11613.31890586
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      candidate_id: 17
      number_of_votes: 17
      latitude: 11613.31890586
      longitude: 11613.31890586
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
                "status": "success",
                "message": "Vote Saved successfully"
            }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/view_candidates
    metadata:
      groupName: Votes
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'View Candidate vot by agent'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      candidate_id:
        name: candidate_id
        description: ''
        required: true
        example: 17
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      candidate_id: 17
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "status": "success",
            "message": "Candidates",
            "vote": {
                "id": 2,
                "created_at": "2025-07-05T09:22:04.000000Z",
                "updated_at": "2025-07-05T09:22:04.000000Z",
                "agent_id": 1,
                "candidate_id": 2,
                "number_of_votes": 23,
                "latitude": 56.45333,
                "longitude": 0.324545
            }
            }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/record_envidence
    metadata:
      groupName: Votes
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'View Evedence'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      picture:
        name: picture
        description: ''
        required: true
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      file_name:
        name: file_name
        description: ''
        required: false
        example: consequatur
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      file_name: consequatur
    fileParameters:
      picture: null
    responses:
      -
        status: 200
        content: |-
          {
            "status": "success",
            "message": "Uploaded files",
            "evedence": [
                {
                    "id": 1,
                    "created_at": "2025-07-05T09:43:49.000000Z",
                    "updated_at": "2025-07-05T09:43:49.000000Z",
                    "agent_id": 1,
                    "file_url": "1751708629XsEueTFKd760.jpg",
                    "file_name": "DR FORM"
                }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/record_envidence
    metadata:
      groupName: Votes
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store Evedence'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      picture:
        name: picture
        description: ''
        required: true
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      file_name:
        name: file_name
        description: ''
        required: false
        example: consequatur
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      file_name: consequatur
    fileParameters:
      picture: null
    responses:
      -
        status: 200
        content: |-
          {
                "status": "success",
                "message": "Evendance uploaded successfully",
                "evedence": {
                    "file_url": "1751708629XsEueTFKd760.jpg",
                    "file_name": "DR FORM",
                    "agent_id": 1,
                    "updated_at": "2025-07-05T09:43:49.000000Z",
                    "created_at": "2025-07-05T09:43:49.000000Z",
                    "id": 1
                }
            }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/record_envidence/{id}'
    metadata:
      groupName: Votes
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete Evedence'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the record envidence.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      eveidence_id:
        name: eveidence_id
        description: ''
        required: true
        example: 17
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      eveidence_id: 17
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
                "status": "success",
                "message": "Evendance deleted successfully"
            }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
