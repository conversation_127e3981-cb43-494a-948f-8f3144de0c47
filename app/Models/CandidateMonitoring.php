<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CandidateMonitoring extends Model
{
    use HasFactory;
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'candidate_monitoring';
    
    protected $fillable = [
        'candidate_id',
        'is_preferred',
        'monitoring_notes',
        'target_votes',
        'vote_gap_alert_threshold'
    ];
    
    /**
     * Get the candidate that is being monitored.
     */
    public function candidate()
    {
        return $this->belongsTo(Candidate::class);
    }
    
    /**
     * Calculate the vote gap between this candidate and the leading candidate,
     * or against the 2nd place candidate if this candidate is leading
     *
     * @return array with gap and competitor
     */
    public function calculateVoteGap()
    {
        $candidate = $this->candidate;
        $position = $candidate->position;
        $candidateVotes = $candidate->totalVotes();

        // Get all candidates in this position with their vote counts
        $candidatesWithVotes = [];
        foreach ($position->candidates as $competitor) {
            $candidatesWithVotes[] = [
                'candidate' => $competitor,
                'votes' => $competitor->totalVotes()
            ];
        }

        // Sort by votes in descending order (highest first)
        usort($candidatesWithVotes, function($a, $b) {
            return $b['votes'] - $a['votes'];
        });

        // Find the preferred candidate's position in the ranking
        $preferredPosition = null;
        foreach ($candidatesWithVotes as $index => $candidateData) {
            if ($candidateData['candidate']->id == $candidate->id) {
                $preferredPosition = $index;
                break;
            }
        }

        $competitor = null;
        $competitorVotes = 0;
        $gap = 0;
        $isLeading = false;

        if ($preferredPosition !== null) {
            if ($preferredPosition === 0) {
                // Preferred candidate is leading, compare against 2nd place
                $isLeading = true;
                if (count($candidatesWithVotes) > 1) {
                    $competitor = $candidatesWithVotes[1]['candidate'];
                    $competitorVotes = $candidatesWithVotes[1]['votes'];
                    $gap = $candidateVotes - $competitorVotes;
                }
            } else {
                // Preferred candidate is not leading, compare against the leader (1st place)
                $isLeading = false;
                $competitor = $candidatesWithVotes[0]['candidate'];
                $competitorVotes = $candidatesWithVotes[0]['votes'];
                $gap = $competitorVotes - $candidateVotes;
            }
        }

        return [
            'gap' => $gap,
            'competitor' => $competitor,
            'preferred_votes' => $candidateVotes,
            'competitor_votes' => $competitorVotes,
            'is_leading' => $isLeading,
            'preferred_position' => $preferredPosition + 1, // 1-based position
            'competitor_position' => $isLeading ? 2 : 1, // Position of the competitor
            'total_candidates' => count($candidatesWithVotes)
        ];
    }
    
    /**
     * Check if the vote gap is below the alert threshold
     * 
     * @return boolean
     */
    public function isGapBelowThreshold()
    {
        if (!$this->vote_gap_alert_threshold) return false;
        
        $gapInfo = $this->calculateVoteGap();
        return $gapInfo['gap'] < $this->vote_gap_alert_threshold;
    }
}
