<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Candidate extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'name',
        'position_id',
        'picture',
        // other existing fillable fields
    ];

    function totalVotes(){
        return Vote::where('candidate_id',$this->id)->sum('number_of_votes');
    }

    function totalStationVotes($polling_station_id) {
        $agents = Agent::where('polling_station_id',$polling_station_id)->pluck('id')->toArray();
        return Vote::where('candidate_id',$this->id)->whereIn('agent_id',$agents)->sum('number_of_votes');
    }
    
    /**
     * Get the monitoring settings for this candidate.
     */
    public function monitoring()
    {
        return $this->hasOne(CandidateMonitoring::class);
    }
    
    /**
     * Check if this candidate is marked as preferred for monitoring
     */
    public function isPreferred()
    {
        return $this->monitoring && $this->monitoring->is_preferred;
    }
    
    /**
     * Get the position this candidate belongs to
     */
    public function position()
    {
        return $this->belongsTo(Position::class);
    }
}
