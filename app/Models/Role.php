<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'permissions',
        'is_active'
    ];

    protected $casts = [
        'permissions' => 'array',
        'is_active' => 'boolean'
    ];

    /**
     * Users that belong to this role
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class)->withTimestamps();
    }

    /**
     * Users who have this as their primary role
     */
    public function primaryUsers(): Has<PERSON>any
    {
        return $this->hasMany(User::class);
    }

    /**
     * Check if role has a specific permission
     */
    public function hasPermission(string $permission): bool
    {
        $permissions = $this->permissions;

        // Handle case where permissions might be stored as JSON string
        if (is_string($permissions)) {
            $permissions = json_decode($permissions, true) ?? [];
        }

        return in_array($permission, $permissions ?? []);
    }

    /**
     * Add permission to role
     */
    public function addPermission(string $permission): void
    {
        $permissions = $this->permissions ?? [];
        if (!in_array($permission, $permissions)) {
            $permissions[] = $permission;
            $this->permissions = $permissions;
            $this->save();
        }
    }

    /**
     * Remove permission from role
     */
    public function removePermission(string $permission): void
    {
        $permissions = $this->permissions ?? [];
        $this->permissions = array_values(array_filter($permissions, fn($p) => $p !== $permission));
        $this->save();
    }

    /**
     * Get all available permissions
     */
    public static function getAllPermissions(): array
    {
        return [
            // Dashboard permissions
            'view_dashboard',
            'view_analytics',
            'view_reports',
            
            // Polling station permissions
            'view_polling_stations',
            'create_polling_stations',
            'edit_polling_stations',
            'delete_polling_stations',
            'manage_polling_stations',
            
            // User management permissions
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',
            'manage_user_roles',
            
            // Agent permissions
            'submit_votes',
            'upload_evidence',
            'view_assigned_station',
            
            // Vote management permissions
            'view_votes',
            'edit_votes',
            'delete_votes',
            'manage_votes',
            
            // Candidate permissions
            'view_candidates',
            'create_candidates',
            'edit_candidates',
            'delete_candidates',
            
            // Position permissions
            'view_positions',
            'create_positions',
            'edit_positions',
            'delete_positions',
            
            // System administration
            'system_settings',
            'backup_restore',
            'view_logs',
            'manage_system'
        ];
    }

    /**
     * Get predefined roles with their permissions
     */
    public static function getPredefinedRoles(): array
    {
        return [
            'admin' => [
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'Full system access with all permissions',
                'permissions' => self::getAllPermissions()
            ],
            'polling_station_manager' => [
                'name' => 'polling_station_manager',
                'display_name' => 'Polling Station Manager',
                'description' => 'Manages polling stations, agents, and vote data',
                'permissions' => [
                    'view_dashboard',
                    'view_analytics',
                    'view_reports',
                    'view_polling_stations',
                    'create_polling_stations',
                    'edit_polling_stations',
                    'delete_polling_stations',
                    'manage_polling_stations',
                    'view_users',
                    'create_users',
                    'edit_users',
                    'view_votes',
                    'edit_votes',
                    'manage_votes',
                    'view_candidates',
                    'view_positions'
                ]
            ],
            'dashboard_viewer' => [
                'name' => 'dashboard_viewer',
                'display_name' => 'Dashboard Viewer',
                'description' => 'Read-only access to dashboard and reports',
                'permissions' => [
                    'view_dashboard',
                    'view_analytics',
                    'view_reports',
                    'view_polling_stations',
                    'view_votes',
                    'view_candidates',
                    'view_positions'
                ]
            ],
            'agent' => [
                'name' => 'agent',
                'display_name' => 'Agent',
                'description' => 'Field agent with vote submission capabilities',
                'permissions' => [
                    'submit_votes',
                    'upload_evidence',
                    'view_assigned_station',
                    'view_candidates',
                    'view_positions'
                ]
            ]
        ];
    }
}
