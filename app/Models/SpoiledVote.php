<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SpoiledVote extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'polling_station_id',
        'position_id',
        'agent_id',
        'number_of_votes',
        'remarks'
    ];
    
    /**
     * Get the polling station that owns the spoiled vote.
     */
    public function pollingStation()
    {
        return $this->belongsTo(PollingStation::class);
    }
    
    /**
     * Get the position that owns the spoiled vote.
     */
    public function position()
    {
        return $this->belongsTo(Position::class);
    }
    
    /**
     * Get the agent that recorded the spoiled vote.
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }
}
