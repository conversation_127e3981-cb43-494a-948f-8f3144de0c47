<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Validator;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'phone_number',
        'password',
        'user_type',
        'role_id',
        'is_active',
        'last_login_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
        'last_login_at' => 'datetime'
    ];


    public static function uploadImage($file)
    {      

        if (empty($file)) 

            return [NULL,NULL];

        $destinationPath = public_path('files');

        $ext = $file->getClientOriginalExtension();

        $file_url=time().Str::random(12).'.'.$ext;

        $file->move($destinationPath,$file_url);
        
        return $file_url;

    }

    public static function getValidationMessage($request,$rules) {

        $val = Validator::make($request->all(),$rules);

        $message = NULL;

        if ($val->fails()) {           

            $errors = $val->errors()->toArray();

            if (isset($errors['name']))

                $message .= $errors['name'][0]." ";
                
            if (isset($errors['password']))

                $message .= $errors['password'][0]." ";

            if (isset($errors['phone_number']))

                $message .= $errors['phone_number'][0]." ";

            if (isset($errors['old_password']))

                $message .= $errors['old_password'][0]." ";

            

        }
        
        return $message;
        
    }

    function lastUpdated() {
        $votes = Vote::latest('updated_at')->first();

        if ($votes) {
            return $votes->updated_at->format('M d, Y h:i A');
        } else {
            return "No vote updated yet.";
        }
    }

    /**
     * Get the primary role that owns the user
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Get all roles that belong to the user
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class)->withTimestamps();
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(string $roleName): bool
    {
        // Admin users have all roles
        if ($this->user_type === 'admin' || ($this->role && $this->role->name === 'admin')) {
            return true;
        }

        // Users automatically have roles based on their user_type
        if ($this->getUserTypeRole() === $roleName) {
            return true;
        }

        // Check primary role
        if ($this->role && $this->role->name === $roleName) {
            return true;
        }

        // Check additional roles
        return $this->roles->contains('name', $roleName);
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole(array $roleNames): bool
    {
        foreach ($roleNames as $roleName) {
            if ($this->hasRole($roleName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if user has all of the given roles
     */
    public function hasAllRoles(array $roleNames): bool
    {
        foreach ($roleNames as $roleName) {
            if (!$this->hasRole($roleName)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Check if user has a specific permission
     */
    public function hasPermission(string $permission): bool
    {
        // Admin users have all permissions
        if ($this->user_type === 'admin' || ($this->role && $this->role->name === 'admin')) {
            return true;
        }

        // Users automatically have permissions based on their user_type
        $userTypePermissions = $this->getUserTypePermissions();
        if (in_array($permission, $userTypePermissions)) {
            return true;
        }

        // Check primary role permissions
        if ($this->role && $this->role->hasPermission($permission)) {
            return true;
        }

        // Check additional roles permissions
        foreach ($this->roles as $role) {
            if ($role->hasPermission($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user has any of the given permissions
     */
    public function hasAnyPermission(array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if user has all of the given permissions
     */
    public function hasAllPermissions(array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Assign a role to the user
     */
    public function assignRole(string $roleName): void
    {
        $role = Role::where('name', $roleName)->first();
        if ($role) {
            $this->roles()->syncWithoutDetaching([$role->id]);
        }
    }

    /**
     * Remove a role from the user
     */
    public function removeRole(string $roleName): void
    {
        $role = Role::where('name', $roleName)->first();
        if ($role) {
            $this->roles()->detach($role->id);
        }
    }

    /**
     * Set the primary role for the user
     */
    public function setPrimaryRole(string $roleName): void
    {
        $role = Role::where('name', $roleName)->first();
        if ($role) {
            $this->role()->associate($role);
            $this->save();
        }
    }

    /**
     * Get permissions based on user type
     */
    public function getUserTypePermissions(): array
    {
        switch ($this->user_type) {
            case 'agent':
                return [
                    'submit_votes',
                    'upload_evidence',
                    'view_assigned_station',
                    'view_candidates',
                    'view_positions'
                ];

            case 'viewer':
                return [
                    'view_dashboard',
                    'view_analytics',
                    'view_reports',
                    'view_polling_stations',
                    'view_votes',
                    'view_candidates',
                    'view_positions'
                ];

            case 'manager':
                return [
                    'view_dashboard',
                    'view_analytics',
                    'view_reports',
                    'view_polling_stations',
                    'create_polling_stations',
                    'edit_polling_stations',
                    'delete_polling_stations',
                    'manage_polling_stations',
                    'view_users',
                    'create_users',
                    'edit_users',
                    'view_votes',
                    'edit_votes',
                    'manage_votes',
                    'view_candidates',
                    'view_positions'
                ];

            default:
                return [];
        }
    }

    /**
     * Get role name based on user type
     */
    public function getUserTypeRole(): ?string
    {
        switch ($this->user_type) {
            case 'agent':
                return 'agent';
            case 'viewer':
                return 'dashboard_viewer';
            case 'manager':
                return 'polling_station_manager';
            case 'admin':
                return 'admin';
            default:
                return null;
        }
    }
}
