<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Position extends Model
{
    use HasFactory;

    function candidates() : HasMany {

        return $this->hasMany(Candidate::class,'position_id');
        
    }

    function totalVotes(){

        $candidateIds = Candidate::where('position_id',$this->id)->pluck('id')->toArray();

        return Vote::whereIn('candidate_id',$candidateIds)->sum('number_of_votes');
        
    }


    function totalStationVotes($polling_station_id){

        $agents = Agent::where('polling_station_id',$polling_station_id)->pluck('id')->toArray();

        $candidateIds = Candidate::where('position_id',$this->id)->pluck('id')->toArray();

        return Vote::whereIn('candidate_id',$candidateIds)->whereIn('agent_id',$agents)->sum('number_of_votes');
        
    }

    function agentsThatWithVotes(){

        $agents = Agent::get();

        $counter = 0;

        foreach ($agents as $agent) {

            $votes = Vote::where('agent_id',$agent->id)->whereIn('candidate_id',$this->candidates->pluck('id')->toArray())->count();

            if($votes > 0)

                $counter ++;
     
        }

        return $counter;
        
    }

    function agents() {

        return Agent::count();
        
    }
}
