<?php

namespace App\Console\Commands;

use App\Models\Candidate;
use App\Models\User;
use App\Notifications\VoteGapAlert;
use Illuminate\Console\Command;

class TriggerTestAlert extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:trigger-test-alert {candidate_id? : The ID of the candidate to trigger an alert for}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Trigger a test vote gap alert notification for demonstration purposes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get candidate ID from argument or prompt for it
        $candidateId = $this->argument('candidate_id');
        
        if (!$candidateId) {
            // List all candidates with their IDs
            $candidates = Candidate::with('position')->get();
            
            $this->info('Available candidates:');
            $headers = ['ID', 'Name', 'Position'];
            $rows = [];
            
            foreach ($candidates as $candidate) {
                $rows[] = [
                    $candidate->id,
                    $candidate->name,
                    $candidate->position->name ?? 'N/A'
                ];
            }
            
            $this->table($headers, $rows);
            $candidateId = $this->ask('Enter the ID of the candidate to trigger an alert for:');
        }
        
        // Find the candidate
        $candidate = Candidate::with(['monitoring', 'position'])->find($candidateId);
        
        if (!$candidate) {
            $this->error("Candidate with ID {$candidateId} not found.");
            return 1;
        }
        
        // Check if candidate has monitoring settings
        if (!$candidate->monitoring) {
            // Create monitoring record if it doesn't exist
            $candidate->monitoring()->create([
                'is_preferred' => true,
                'vote_gap_alert_threshold' => 100,
            ]);
            $candidate->refresh();
            $this->info("Created monitoring settings for {$candidate->name}.");
        }
        
        // Create a simulated gap info array
        $competitors = Candidate::where('position_id', $candidate->position_id)
                               ->where('id', '!=', $candidate->id)
                               ->get();
        
        if ($competitors->isEmpty()) {
            $this->error("No competitors found for {$candidate->name}. Cannot create a realistic alert.");
            return 1;
        }
        
        $competitor = $competitors->first();
        $isLeading = $this->choice(
            "Should {$candidate->name} be leading or trailing?",
            ['leading', 'trailing'],
            0
        ) === 'leading';
        
        $gap = $this->ask('Enter the vote gap to simulate (e.g., 50):', 50);
        
        $gapInfo = [
            'preferred_votes' => $isLeading ? 1000 : 950,
            'competitor_votes' => $isLeading ? 950 : 1000,
            'competitor' => $competitor,
            'gap' => $gap,
            'is_leading' => $isLeading,
        ];
        
        // Send notification to all admin users
        $admins = User::where('user_type', 'admin')->get();
        
        if ($admins->isEmpty()) {
            $this->error('No admin users found to send notifications to.');
            return 1;
        }
        
        foreach ($admins as $admin) {
            $admin->notify(new VoteGapAlert($candidate, $gapInfo));
        }
        
        $this->info('Test alert notification sent successfully!');
        $this->line("Candidate: {$candidate->name}");
        $this->line("Position: {$candidate->position->name}");
        $this->line("Status: " . ($isLeading ? 'Leading by' : 'Trailing by') . " {$gap} votes");
        $this->line("Recipients: " . $admins->pluck('name')->implode(', '));
        
        return 0;
    }
}
