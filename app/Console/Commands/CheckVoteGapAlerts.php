<?php

namespace App\Console\Commands;

use App\Models\Candidate;
use App\Models\User;
use App\Notifications\VoteGapAlert;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckVoteGapAlerts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-vote-gap-alerts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check vote gaps for preferred candidates and send alerts if below threshold';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking vote gaps for preferred candidates...');
        
        // Get all preferred candidates with monitoring settings
        $preferredCandidates = Candidate::whereHas('monitoring', function($query) {
            $query->where('is_preferred', true)
                  ->whereNotNull('vote_gap_alert_threshold');
        })->with(['monitoring', 'position'])->get();
        
        if ($preferredCandidates->isEmpty()) {
            $this->info('No preferred candidates with alert thresholds found.');
            return 0;
        }
        
        $this->info('Found ' . $preferredCandidates->count() . ' preferred candidates with alert thresholds.');
        
        $alertsSent = 0;
        
        // Check each candidate's vote gap
        foreach ($preferredCandidates as $candidate) {
            $gapInfo = $candidate->monitoring->calculateVoteGap();
            $threshold = $candidate->monitoring->vote_gap_alert_threshold;
            
            // If gap is below threshold, send alert
            if ($gapInfo['gap'] < $threshold) {
                $this->warn(
                    'Alert threshold triggered for ' . $candidate->name . 
                    ' - Gap: ' . $gapInfo['gap'] . 
                    ' (Threshold: ' . $threshold . ')'  
                );
                
                // Send notification to all admin users
                $admins = User::where('user_type', 'admin')->get();
                
                foreach ($admins as $admin) {
                    $admin->notify(new VoteGapAlert($candidate, $gapInfo));
                }
                
                $alertsSent++;
                
                // Log the alert
                Log::channel('daily')->info(
                    'Vote gap alert triggered for ' . $candidate->name . 
                    ' with gap of ' . $gapInfo['gap'] . 
                    ' votes against ' . ($gapInfo['competitor'] ? $gapInfo['competitor']->name : 'no competitor')
                );
            }
        }
        
        $this->info('Alerts sent: ' . $alertsSent);
        return 0;
    }
}
