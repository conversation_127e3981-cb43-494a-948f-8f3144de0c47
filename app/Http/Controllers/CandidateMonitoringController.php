<?php

namespace App\Http\Controllers;

use App\Models\Candidate;
use App\Models\CandidateMonitoring;
use App\Models\Position;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CandidateMonitoringController extends Controller
{
    /**
     * Display the candidate monitoring dashboard.
     */
    public function index()
    {
        $positions = Position::with(['candidates' => function($query) {
            $query->with('monitoring');
        }])->get();
        
        $preferredCandidates = Candidate::whereHas('monitoring', function($query) {
            $query->where('is_preferred', true);
        })->with(['monitoring', 'position'])->get();
        
        // Calculate gap information for each preferred candidate
        $monitoringData = [];
        foreach ($preferredCandidates as $candidate) {
            $gapInfo = $candidate->monitoring->calculateVoteGap();
            $monitoringData[$candidate->id] = $gapInfo;
        }
        
        return view('monitoring.index', compact('positions', 'preferredCandidates', 'monitoringData'));
    }
    
    /**
     * Add a candidate to preferred monitoring.
     */
    public function addPreferred(Request $request, Candidate $candidate)
    {
        // Get or create monitoring record
        $monitoring = CandidateMonitoring::firstOrNew(['candidate_id' => $candidate->id]);
        $monitoring->is_preferred = true;
        $monitoring->save();

        return back()->with('success', "Added {$candidate->name} to monitoring.");
    }

    /**
     * Remove a candidate from preferred monitoring.
     */
    public function removePreferred(Request $request, Candidate $candidate)
    {
        // Find monitoring record
        $monitoring = CandidateMonitoring::where('candidate_id', $candidate->id)->first();

        if ($monitoring) {
            $monitoring->is_preferred = false;
            $monitoring->save();
        }

        return back()->with('success', "Removed {$candidate->name} from monitoring.");
    }

    /**
     * Toggle a candidate's preferred status.
     */
    public function togglePreferred(Request $request, Candidate $candidate)
    {
        // Get or create monitoring record
        $monitoring = CandidateMonitoring::firstOrNew(['candidate_id' => $candidate->id]);

        // Toggle preferred status
        $monitoring->is_preferred = !$monitoring->is_preferred;
        $monitoring->save();

        return back()->with('success', $monitoring->is_preferred ?
            "Added {$candidate->name} to preferred monitoring." :
            "Removed {$candidate->name} from preferred monitoring.");
    }
    
    /**
     * Update monitoring settings for a candidate.
     */
    public function updateSettings(Request $request, Candidate $candidate)
    {
        $validated = $request->validate([
            'monitoring_notes' => 'nullable|string',
            'target_votes' => 'nullable|integer|min:0',
            'vote_gap_alert_threshold' => 'nullable|integer|min:0',
        ]);
        
        // Get or create monitoring record
        $monitoring = CandidateMonitoring::firstOrNew(['candidate_id' => $candidate->id]);
        $monitoring->fill($validated);
        $monitoring->save();
        
        return back()->with('success', "Updated monitoring settings for {$candidate->name}.");
    }
    
    /**
     * Show the comparison view between preferred candidate and competitors.
     */
    public function compare(Candidate $candidate)
    {
        $candidate->load(['monitoring', 'position.candidates']);
        
        if (!$candidate->monitoring || !$candidate->monitoring->is_preferred) {
            return redirect()->route('monitoring.index')
                ->with('error', "This candidate is not marked for preferred monitoring.");
        }
        
        $gapInfo = $candidate->monitoring->calculateVoteGap();
        $position = $candidate->position;
        $competitors = $position->candidates->where('id', '!=', $candidate->id);
        
        // Get polling station breakdown
        $pollingStations = \App\Models\PollingStation::all();
        $stationBreakdown = [];
        
        foreach ($pollingStations as $station) {
            $candidateVotes = $candidate->totalStationVotes($station->id);
            $competitorVotes = [];
            
            foreach ($competitors as $competitor) {
                $competitorVotes[$competitor->id] = $competitor->totalStationVotes($station->id);
            }
            
            $stationBreakdown[$station->id] = [
                'station' => $station,
                'candidate_votes' => $candidateVotes,
                'competitor_votes' => $competitorVotes
            ];
        }
        
        return view('monitoring.compare', compact(
            'candidate',
            'gapInfo',
            'position',
            'competitors',
            'stationBreakdown'
        ));
    }

    /**
     * Get monitoring data for AJAX refresh
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMonitoringData()
    {
        $preferredCandidates = Candidate::whereHas('monitoring', function($query) {
            $query->where('is_preferred', true);
        })->with(['monitoring', 'position'])->get();

        // Calculate gap information for each preferred candidate
        $monitoringData = [];
        foreach ($preferredCandidates as $candidate) {
            $gapInfo = $candidate->monitoring->calculateVoteGap();
            $monitoringData[$candidate->id] = $gapInfo;
        }

        // Calculate statistics
        $leadingCount = 0;
        $trailingCount = 0;
        $alertsCount = 0;

        foreach($preferredCandidates as $candidate) {
            $gapInfo = $monitoringData[$candidate->id] ?? null;
            if (!$gapInfo) continue;

            if ($gapInfo['is_leading']) {
                $leadingCount++;
            } else {
                $trailingCount++;
            }

            $alertThreshold = $candidate->monitoring->vote_gap_alert_threshold;
            if ($alertThreshold && $gapInfo['gap'] < $alertThreshold) {
                $alertsCount++;
            }
        }

        return response()->json([
            'candidates' => $preferredCandidates,
            'monitoring_data' => $monitoringData,
            'stats' => [
                'leading_count' => $leadingCount,
                'trailing_count' => $trailingCount,
                'alerts_count' => $alertsCount,
                'total_preferred' => $preferredCandidates->count()
            ],
            'last_updated' => now()->toISOString()
        ]);
    }
}
