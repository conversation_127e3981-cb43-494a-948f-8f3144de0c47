<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\PollingStation;
use App\Models\Position;
use App\Models\SpoiledVote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SpoiledVoteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $spoiledVotes = SpoiledVote::with(['pollingStation', 'position', 'agent.user'])->latest()->get();
        return view('spoiled_votes.index', compact('spoiledVotes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $positions = Position::all();
        $pollingStations = PollingStation::all();
        
        // If user is an agent, pre-select their polling station
        $preselectedStation = null;
        if (Auth::user()->user_type == 'agent') {
            $agent = Agent::where('user_id', Auth::id())->first();
            if ($agent && $agent->polling_station_id) {
                $preselectedStation = $agent->polling_station_id;
            }
        }
        
        return view('spoiled_votes.create', compact('positions', 'pollingStations', 'preselectedStation'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'polling_station_id' => 'required|exists:polling_stations,id',
            'position_id' => 'required|exists:positions,id',
            'number_of_votes' => 'required|integer|min:0',
            'remarks' => 'nullable|string'
        ]);
        
        // Get the agent ID based on the authenticated user
        $agentId = null;
        if (Auth::user()->user_type == 'agent') {
            $agent = Agent::where('user_id', Auth::id())->first();
            if ($agent) {
                $agentId = $agent->id;
            }
        } else {
            // For admin users, they can specify the agent
            $request->validate(['agent_id' => 'required|exists:agents,id']);
            $agentId = $request->agent_id;
        }
        
        // Create the spoiled vote record
        SpoiledVote::create([
            'polling_station_id' => $request->polling_station_id,
            'position_id' => $request->position_id,
            'agent_id' => $agentId,
            'number_of_votes' => $request->number_of_votes,
            'remarks' => $request->remarks
        ]);
        
        return redirect()->route('spoiled_votes.index')
            ->with('success', 'Spoiled votes recorded successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $spoiledVote = SpoiledVote::with(['pollingStation', 'position', 'agent.user'])->findOrFail($id);
        return view('spoiled_votes.show', compact('spoiledVote'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $spoiledVote = SpoiledVote::findOrFail($id);
        $positions = Position::all();
        $pollingStations = PollingStation::all();
        
        return view('spoiled_votes.edit', compact('spoiledVote', 'positions', 'pollingStations'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $spoiledVote = SpoiledVote::findOrFail($id);
        
        $request->validate([
            'polling_station_id' => 'required|exists:polling_stations,id',
            'position_id' => 'required|exists:positions,id',
            'number_of_votes' => 'required|integer|min:0',
            'remarks' => 'nullable|string'
        ]);
        
        $spoiledVote->update([
            'polling_station_id' => $request->polling_station_id,
            'position_id' => $request->position_id,
            'number_of_votes' => $request->number_of_votes,
            'remarks' => $request->remarks
        ]);
        
        return redirect()->route('spoiled_votes.index')
            ->with('success', 'Spoiled votes updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $spoiledVote = SpoiledVote::findOrFail($id);
        $spoiledVote->delete();
        
        return redirect()->route('spoiled_votes.index')
            ->with('success', 'Spoiled votes record deleted successfully.');
    }
}
