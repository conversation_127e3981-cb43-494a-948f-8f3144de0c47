<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AgentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $rules = [
            'name'=>'required',
            'phone_number'=>'required'
        ];

        $this->validate($request,$rules);

        $users = User::where('phone_number',$request->phone_number)->get();

        if($users->count() == 0)

            $user = new User();

        else

            $user = $users->last();        

        $user->name = $request->name;

        $user->phone_number = $request->phone_number;

        $user->user_type = "agent";

        $user->password = Hash::make($request->phone_number);

        $user->save();

        $check_agents = Agent::where('polling_station_id',$request->polling_station_id)->get();

        if($check_agents->count() == 0){

            $saveAgent = new Agent();

        }else{

            $saveAgent = $check_agents->last();

        }        

        $saveAgent->user_id = $user->id;

        $saveAgent->polling_station_id = $request->polling_station_id;

        $saveAgent->save();

        return redirect()->route('polling_stations.show',$saveAgent->polling_station_id);

    }

    /**
     * Display the specified resource.
     */
    public function show(Agent $agent)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Agent $agent)
    {
        $data = [
            'agent'=>$agent,
            'title'=>'Edit agent'
        ];

        return view('polling_stations.edit_agent')->with($data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $agent_id)
    {
        $agent = Agent::findOrFail($agent_id);

        $agent->user->name = $request->name;

        $agent->user->phone_number = $request->phone_number;

        $agent->user->save();

        return redirect()->route('polling_stations.show',$agent->polling_station_id);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($agent_id)
    {
        Agent::destroy($agent_id);
        return back();
    }
}
