<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\Vote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class VoteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /** 
     * Record Vote
     * @group Votes 
     * @bodyParam candidate_id integer required
     * @bodyParam number_of_votes integer required
     * @bodyParam latitude double
     * @bodyParam longitude double
     * @response   {
     *       "status": "success",
     *       "message": "Vote Saved successfully"
     *   }
    **/
    public function store(Request $request)
    {
        
        $rules = [
            'candidate_id'=>'required',
            'number_of_votes'=>'required|numeric',
            'latitude'=>'nullable|numeric',
            'longitude'=>'nullable|numeric',
        ];

        $this->validate($request,$rules);

        $agent = Agent::where('user_id',Auth::id())->get()->last();

        if(!$agent){       

            return  response()->json(['status' => 'failed', 'message' => 'Only Agents can post Election results'],422);

        }

        $check_votes = Vote::where('agent_id',$agent->id)->where('candidate_id',$request->candidate_id)->get();

        if($check_votes->count() == 0){

            $saveVote = new Vote();

        }else{

            $saveVote = $check_votes->last();

        }        

        $saveVote->agent_id = $agent->id;

        $saveVote->candidate_id = $request->candidate_id;

        $saveVote->number_of_votes = $request->number_of_votes;

        $saveVote->latitude = $request->latitude;

        $saveVote->longitude = $request->longitude;

        $saveVote->save();

        if($request->ajax()){

            return  response()->json(['status' => 'success', 'message' => 'Vote Saved','data'=>$saveVote],200);

        }

        return back();


    }

    /**
     * Display the specified resource.
     */
    public function show(Vote $vote)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Vote $vote)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Vote $vote)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vote $vote)
    {
        //
    }
}
