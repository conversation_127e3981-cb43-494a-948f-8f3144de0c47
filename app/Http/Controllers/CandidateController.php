<?php

namespace App\Http\Controllers;

use App\Models\Candidate;
use App\Models\User;
use Illuminate\Http\Request;

class CandidateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
      
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $rules = [
            'name'=>'required',
            'name'=>'required'
        ];

        $this->validate($request,$rules);

        $candidate = new Candidate();

        $candidate->name = $request->name;

        $candidate->position_id = $request->position_id;

        if($request->hasFile('picture')){

            $candidate->picture =  User::uploadImage($request->file('picture'));

        }        

        $candidate->party_color = $request->party_color;

        $candidate->party_name = $request->party_name;

        try {

           $candidate->save();

           return redirect()->route('positions.show',$candidate->position_id);

        } catch (\Exception $th) {

            return back()->with(['status'=>$th->getMessage()]);

        }        

    }

    /**
     * Display the specified resource.
     */
    public function show(Candidate $candidate)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Candidate $candidate)
    {
        $data = [
            'candidate'=>$candidate,
            'title'=>'Edit Candidate'
        ];

        return view('candidates.edit_candidate')->with($data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $candidate_id)
    {
        
        $candidate = Candidate::findOrFail($candidate_id);

        $candidate->name = $request->name;

        if($request->hasFile('picture')){

            $candidate->picture =  User::uploadImage($request->file('picture'));

        }

        $candidate->party_color = $request->party_color;

        $candidate->party_name = $request->party_name;

        try {

           $candidate->save();

           return redirect()->route('positions.show',$candidate->position_id);

        } catch (\Exception $th) {

            return back()->with(['status'=>$th->getMessage()]);

        }    
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($candidate_id)
    {
        try {
            $candidate = Candidate::findOrFail($candidate_id);
            $position_id = $candidate->position_id;
            $candidate->delete();
            
            return redirect()->route('positions.show', $position_id)
                ->with('success', 'Candidate deleted successfully');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete candidate: ' . $e->getMessage());
        }
    }
}
