<?php

namespace App\Http\Controllers;

use App\Models\Candidate;
use App\Models\Position;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

use function PHPUnit\Framework\returnSelf;

class PositionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        if(Auth::user()->user_type == "admin"){

            $positions = Position::get();

            $data = [
                'positions'=>$positions,
                'title'=>'Positions'
            ];

            return view('positions.list')->with($data);

        }

        return redirect('home');
       
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $rules = [
            'name'=>'required'
        ];

        $this->validate($request,$rules);

        $savePosition = new Position();

        $savePosition->name = $request->name;

        $savePosition->save();

        return redirect()->route('positions.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(Position $position)
    {

        if(Auth::user()->user_type != "admin") return redirect('home');

        $candidates = Candidate::where('position_id',$position->id)->get();

        $data = [
            'candidates'=>$candidates,
            'title'=>$position->name.' Candidates',
            'position'=>$position
        ];

        return view('candidates.list')->with($data);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Position $position)
    {
        $data = [
            'position'=>$position,
            'title'=>'Edit Position'
        ];

        return view('positions.edit_position')->with($data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $position_id)
    {
        $savePosition = Position::findOrFail($position_id);

        $savePosition->name = $request->name;

        $savePosition->save();

        return redirect()->route('positions.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($position_id)
    {
        try {
            Position::destroy($position_id);
        } catch (\Throwable $th) {}

        return back();
    }

    function ajaxGetCandidates($candidate_id) {

        return Candidate::where('position_id',$candidate_id)->get();
        
    }
}
