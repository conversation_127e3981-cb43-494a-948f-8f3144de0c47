<?php

namespace App\Http\Controllers;

use App\Models\Role;
use Illuminate\Http\Request;

class RoleController extends Controller
{
    /**
     * Display a listing of roles and their permissions
     */
    public function index()
    {
        $roles = Role::where('is_active', true)->with('users')->get();
        $allPermissions = Role::getAllPermissions();
        
        return view('admin.roles.index', compact('roles', 'allPermissions'));
    }

    /**
     * Show the form for creating a new role
     */
    public function create()
    {
        $allPermissions = Role::getAllPermissions();
        return view('admin.roles.create', compact('allPermissions'));
    }

    /**
     * Store a newly created role
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'array',
            'permissions.*' => 'string|in:' . implode(',', Role::getAllPermissions()),
            'is_active' => 'boolean'
        ]);

        Role::create([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
            'permissions' => $request->permissions ?? [],
            'is_active' => $request->boolean('is_active', true)
        ]);

        return redirect()->route('admin.roles.index')
            ->with('success', 'Role created successfully.');
    }

    /**
     * Display the specified role
     */
    public function show(Role $role)
    {
        $role->load('users');
        return view('admin.roles.show', compact('role'));
    }

    /**
     * Show the form for editing the specified role
     */
    public function edit(Role $role)
    {
        $allPermissions = Role::getAllPermissions();
        return view('admin.roles.edit', compact('role', 'allPermissions'));
    }

    /**
     * Update the specified role
     */
    public function update(Request $request, Role $role)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'array',
            'permissions.*' => 'string|in:' . implode(',', Role::getAllPermissions()),
            'is_active' => 'boolean'
        ]);

        $role->update([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
            'permissions' => $request->permissions ?? [],
            'is_active' => $request->boolean('is_active', true)
        ]);

        return redirect()->route('admin.roles.index')
            ->with('success', 'Role updated successfully.');
    }

    /**
     * Remove the specified role
     */
    public function destroy(Role $role)
    {
        // Prevent deletion of predefined roles
        $predefinedRoles = ['admin', 'polling_station_manager', 'dashboard_viewer', 'agent'];
        if (in_array($role->name, $predefinedRoles)) {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Cannot delete predefined system roles.');
        }

        // Check if role has users
        if ($role->users()->count() > 0) {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Cannot delete role that has assigned users.');
        }

        $role->delete();

        return redirect()->route('admin.roles.index')
            ->with('success', 'Role deleted successfully.');
    }
}
