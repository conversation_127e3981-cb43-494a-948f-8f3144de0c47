<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\PollingStation;
use App\Models\Position;
use App\Models\SpoiledVote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Get simple vote trends data for candidates
     *
     * @param int $positionId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getVoteTrendsData($positionId = 'overall')
    {
        // Simple time intervals throughout the day
        $timeLabels = [
            '8:00', '9:00', '10:00', '11:00', '12:00', '13:00',
            '14:00', '15:00', '16:00', '17:00', '18:00'
        ];

        // Prepare simple data structure
        $voteTrendsData = [
            'labels' => $timeLabels,
            'datasets' => [],
            'metadata' => [
                'totalVotes' => 0,
                'lastUpdate' => now()->toISOString(),
                'positionName' => $positionId === 'overall' ? 'All Positions' : null
            ]
        ];

        // Simple color palette
        $colors = [
            '#3498DB', // Blue
            '#E74C3C', // Red
            '#2ECC71', // Green
            '#F39C12', // Orange
            '#9B59B6', // Purple
            '#1ABC9C'  // Teal
        ];

        // Get candidates based on position
        if ($positionId === 'overall') {
            // Get top candidates across all positions
            $allCandidates = \App\Models\Candidate::with('position')->get();

            // Calculate vote counts for each candidate
            $candidatesWithVotes = $allCandidates->map(function($candidate) {
                $voteCount = \App\Models\Vote::where('candidate_id', $candidate->id)->sum('number_of_votes');
                $candidate->current_votes = $voteCount;
                return $candidate;
            });

            // Get top candidates by vote count (up to 6 for better visualization)
            $topCandidates = $candidatesWithVotes->sortByDesc('current_votes')->take(6)->values();
            $voteTrendsData['metadata']['totalVotes'] = $candidatesWithVotes->sum('current_votes');
        } else {
            // Get candidates for a specific position
            $position = Position::findOrFail($positionId);
            $candidates = $position->candidates()->get();

            // Calculate vote counts for each candidate in this position
            $topCandidates = $candidates->map(function($candidate) {
                $voteCount = \App\Models\Vote::where('candidate_id', $candidate->id)->sum('number_of_votes');
                $candidate->current_votes = $voteCount;
                return $candidate;
            })->sortByDesc('current_votes')->values();

            $voteTrendsData['metadata']['positionName'] = $position->name;
            $voteTrendsData['metadata']['totalVotes'] = $topCandidates->sum('current_votes');
        }

        // Generate simple vote trends datasets
        foreach ($topCandidates as $index => $candidate) {
            $voteCounts = [];
            $currentVotes = $candidate->current_votes ?? 0;

            // Generate realistic vote progression throughout the day
            foreach ($timeLabels as $timeIndex => $time) {
                if ($timeIndex === 0) {
                    // Start with a small percentage of final votes
                    $voteCounts[] = max(0, round($currentVotes * 0.1));
                } else {
                    // Gradually increase votes with some randomness
                    $progressRatio = $timeIndex / (count($timeLabels) - 1);
                    $baseVotes = round($currentVotes * $progressRatio);

                    // Add some realistic variation (±5%)
                    $variation = round($baseVotes * (rand(-5, 5) / 100));
                    $voteCounts[] = max(0, $baseVotes + $variation);
                }
            }

            // Ensure final count matches current actual votes
            $voteCounts[count($voteCounts) - 1] = $currentVotes;

            // Create simple dataset
            $voteTrendsData['datasets'][] = [
                'label' => $candidate->name . ($positionId === 'overall' && isset($candidate->position) ? ' (' . $candidate->position->name . ')' : ''),
                'data' => $voteCounts,
                'borderColor' => $colors[$index % count($colors)],
                'backgroundColor' => 'rgba(' . $this->hexToRgb($colors[$index % count($colors)]) . ', 0.1)',
                'metadata' => [
                    'candidateId' => $candidate->id,
                    'currentVotes' => $currentVotes,
                    'positionName' => isset($candidate->position) ? $candidate->position->name : 'Unknown'
                ]
            ];
        }

        return response()->json($voteTrendsData);
    }



    /**
     * Convert hex color to RGB
     *
     * @param string $hex
     * @return string
     */
    private function hexToRgb($hex) {
        $hex = str_replace('#', '', $hex);
        
        if(strlen($hex) == 3) {
            $r = hexdec(substr($hex, 0, 1).substr($hex, 0, 1));
            $g = hexdec(substr($hex, 1, 1).substr($hex, 1, 1));
            $b = hexdec(substr($hex, 2, 1).substr($hex, 2, 1));
        } else {
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
        }
        
        return "$r, $g, $b";
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index(Request $request)
    {
        if(Auth::user()->user_type == "agent"){
            return redirect('agent_dashboard');
        }

        // Get positions with optimized loading
        $positions = Position::with(['candidates' => function($query) {
            $query->with('monitoring');
        }])->get();

        // Build polling stations query with filters
        $pollingStationsQuery = PollingStation::with(['agents.user']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $pollingStationsQuery->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('district', 'like', "%{$search}%")
                      ->orWhere('county', 'like', "%{$search}%")
                      ->orWhere('subcounty', 'like', "%{$search}%")
                      ->orWhere('parish', 'like', "%{$search}%")
                      ->orWhere('village', 'like', "%{$search}%");
            });
        }

        if ($request->filled('district')) {
            $pollingStationsQuery->where('district', $request->get('district'));
        }

        if ($request->filled('county')) {
            $pollingStationsQuery->where('county', $request->get('county'));
        }

        if ($request->filled('subcounty')) {
            $pollingStationsQuery->where('subcounty', $request->get('subcounty'));
        }

        if ($request->filled('coordinates')) {
            if ($request->get('coordinates') === 'with') {
                $pollingStationsQuery->whereNotNull('latitude')->whereNotNull('longitude');
            } elseif ($request->get('coordinates') === 'without') {
                $pollingStationsQuery->where(function($query) {
                    $query->whereNull('latitude')->orWhereNull('longitude');
                });
            }
        }

        if ($request->filled('agents')) {
            if ($request->get('agents') === 'with') {
                $pollingStationsQuery->whereHas('agents');
            } elseif ($request->get('agents') === 'without') {
                $pollingStationsQuery->whereDoesntHave('agents');
            }
        }

        if ($request->filled('results')) {
            if ($request->get('results') === 'submitted') {
                $pollingStationsQuery->whereHas('agents.votes');
            } elseif ($request->get('results') === 'pending') {
                $pollingStationsQuery->whereHas('agents')->whereDoesntHave('agents.votes');
            }
        }

        if ($request->filled('evidence')) {
            if ($request->get('evidence') === 'with') {
                $pollingStationsQuery->whereHas('agents.eveidences');
            } elseif ($request->get('evidence') === 'without') {
                $pollingStationsQuery->whereHas('agents')->whereDoesntHave('agents.eveidences');
            }
        }

        // Get pagination parameters
        $perPage = $request->get('per_page', 15);
        $perPage = in_array($perPage, [10, 15, 25, 50, 100]) ? $perPage : 15;

        // Paginate results
        $polling_stations = $pollingStationsQuery->paginate($perPage)->withQueryString();

        // Get filter options for dropdowns
        $districts = PollingStation::distinct()->whereNotNull('district')->pluck('district')->sort();
        $counties = PollingStation::distinct()->whereNotNull('county')->pluck('county')->sort();
        $subcounties = PollingStation::distinct()->whereNotNull('subcounty')->pluck('subcounty')->sort();

        // Get preferred candidates for monitoring section
        $preferredCandidates = \App\Models\Candidate::whereHas('monitoring', function($query) {
            $query->where('is_preferred', true);
        })->with(['monitoring', 'position'])->get();

        // Calculate gap information for each preferred candidate
        $monitoringData = [];
        foreach ($preferredCandidates as $candidate) {
            if ($candidate->monitoring) {
                $monitoringData[$candidate->id] = $candidate->monitoring->calculateVoteGap();
            }
        }

        // Get spoiled votes summary data (optimized)
        $spoiledVotesTotal = SpoiledVote::sum('number_of_votes');
        $spoiledVotesByPosition = SpoiledVote::with('position')
            ->select('position_id', DB::raw('SUM(number_of_votes) as total'))
            ->groupBy('position_id')
            ->get()
            ->keyBy('position_id');

        $spoiledVotesByStation = SpoiledVote::with('pollingStation')
            ->select('polling_station_id', DB::raw('SUM(number_of_votes) as total'))
            ->groupBy('polling_station_id')
            ->get()
            ->keyBy('polling_station_id');

        $data = [
            'positions' => $positions,
            'title' => 'Dashboard',
            'polling_stations' => $polling_stations,
            'districts' => $districts,
            'counties' => $counties,
            'subcounties' => $subcounties,
            'preferredCandidates' => $preferredCandidates,
            'monitoringData' => $monitoringData,
            'spoiledVotesTotal' => $spoiledVotesTotal,
            'spoiledVotesByPosition' => $spoiledVotesByPosition,
            'spoiledVotesByStation' => $spoiledVotesByStation,
            'currentFilters' => $request->only(['search', 'district', 'county', 'subcounty', 'coordinates', 'agents', 'results', 'evidence', 'per_page'])
        ];

        return view('home')->with($data);
    }

    function agentDashboard(){

        $positions = Position::get();

        $agent = Agent::where('user_id',Auth::id())->get()->last();

        $data = [

            'positions'=>$positions,
            'agent'=>$agent,

        ];

       return view('agent_home')->with($data);
        
    }
}
