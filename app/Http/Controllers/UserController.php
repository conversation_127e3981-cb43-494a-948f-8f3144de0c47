<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = [
            'title'=>'Change password'
        ];

        return view('change_password')->with($data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
         $rules = [
            'old_password' => 'required',
            'password' => 'required|string|confirmed',
        ];

        $message = User::getValidationMessage($request,$rules);

        if(!empty($message))

            return back()->with(['status'=>$message]);
            
            
        if (Hash::check($request->old_password, Auth::user()->password)) {

            $user = Auth::user();

            $user->password = Hash::make($request->old_password);

            $user->save();

             return back()->with(['status'=>'Password changes']);
         
        } 

         return back()->with(['status'=>'Old password is wrong']);
     
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
