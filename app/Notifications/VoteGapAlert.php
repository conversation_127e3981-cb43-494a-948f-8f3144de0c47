<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class VoteGapAlert extends Notification implements ShouldQueue
{
    use Queueable;
    
    protected $candidate;
    protected $gapInfo;

    /**
     * Create a new notification instance.
     */
    public function __construct($candidate, $gapInfo)
    {
        $this->candidate = $candidate;
        $this->gapInfo = $gapInfo;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $competitor = $this->gapInfo['competitor'];
        $gap = $this->gapInfo['gap'];
        $isLeading = $this->gapInfo['is_leading'];
        $position = $this->candidate->position->name;
        
        return (new MailMessage)
                    ->subject('Vote Gap Alert: ' . $this->candidate->name)
                    ->greeting('Vote Gap Alert!')
                    ->line("This is an alert regarding your preferred candidate: {$this->candidate->name} for {$position}.")
                    ->line($isLeading 
                        ? "Their lead has fallen to only {$gap} votes against {$competitor->name}." 
                        : "They are trailing behind {$competitor->name} by {$gap} votes.")
                    ->action('View Detailed Comparison', route('monitoring.compare', $this->candidate->id))
                    ->line('This alert was triggered because the vote gap is below your configured threshold.')
                    ->line('Thank you for using Vote Count!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $competitor = $this->gapInfo['competitor'];
        $gap = $this->gapInfo['gap'];
        $isLeading = $this->gapInfo['is_leading'];
        
        return [
            'candidate_id' => $this->candidate->id,
            'candidate_name' => $this->candidate->name,
            'position' => $this->candidate->position->name,
            'competitor_name' => $competitor ? $competitor->name : null,
            'gap' => $gap,
            'is_leading' => $isLeading,
            'threshold' => $this->candidate->monitoring->vote_gap_alert_threshold,
            //
        ];
    }
}
