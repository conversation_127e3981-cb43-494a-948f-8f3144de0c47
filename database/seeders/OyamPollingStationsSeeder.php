<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PollingStation;
use App\Models\Agent;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class OyamPollingStationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Oyam District boundaries (approximate)
        $oyamBounds = [
            'minLat' => 2.0,
            'maxLat' => 2.6,
            'minLng' => 32.2,
            'maxLng' => 33.0
        ];

        // Oyam subcounties and parishes
        $locations = [
            'Acaba' => ['Acaba', 'Abok', 'Agweng', 'Akidi', 'Aleka'],
            'Aber' => ['Aber', 'Aboke', 'Agwata', 'Akwang', 'Atyak'],
            'Ajuri' => ['Ajuri', 'Adwari', 'Agago', 'Akura', 'Amugu'],
            '<PERSON>eke' => ['<PERSON>ek<PERSON>', '<PERSON>ba<PERSON>', '<PERSON><PERSON>', 'Agweng', 'Akello'],
            '<PERSON>me' => ['<PERSON><PERSON>', 'Abwoch', 'Adyel', 'Agwata', 'Akwang'],
            'Kamdini' => ['Kamdini', 'Aboke', 'Aduku', 'Agago', 'Akello'],
            'Minakulu' => ['Minakulu', 'Abwoch', 'Adyel', 'Agweng', 'Akidi'],
            'Myene' => ['Myene', 'Abako', 'Adwari', 'Agwata', 'Atyak'],
            'Ngai' => ['Ngai', 'Abok', 'Aduku', 'Akura', 'Amugu'],
            'Otuke' => ['Otuke', 'Aboke', 'Agago', 'Akwang', 'Akello'],
            'Oyam' => ['Oyam', 'Abwoch', 'Adyel', 'Agweng', 'Akidi'],
        ];

        // Common Ugandan names for agents
        $firstNames = [
            'James', 'Mary', 'John', 'Sarah', 'David', 'Grace', 'Peter', 'Jane',
            'Paul', 'Rose', 'Moses', 'Ruth', 'Samuel', 'Joyce', 'Daniel', 'Agnes',
            'Joseph', 'Margaret', 'Michael', 'Catherine', 'Robert', 'Betty', 'Francis',
            'Alice', 'Emmanuel', 'Florence', 'Isaac', 'Esther', 'Charles', 'Helen',
            'Patrick', 'Susan', 'Stephen', 'Christine', 'Richard', 'Josephine',
            'Anthony', 'Winnie', 'George', 'Stella', 'Martin', 'Doreen', 'Fred',
            'Irene', 'Simon', 'Lydia', 'Andrew', 'Brenda', 'Kenneth', 'Mercy'
        ];

        $lastNames = [
            'Okello', 'Akello', 'Obwona', 'Aceng', 'Odongo', 'Akot', 'Opio',
            'Aber', 'Ocen', 'Akumu', 'Otim', 'Auma', 'Ogwal', 'Atim', 'Okot',
            'Adong', 'Olanya', 'Awor', 'Ocan', 'Apio', 'Omara', 'Akech', 'Ojok',
            'Anek', 'Olum', 'Awino', 'Ocitti', 'Apiyo', 'Omoding', 'Akello',
            'Onen', 'Aweko', 'Odoch', 'Atoo', 'Olweny', 'Acan', 'Okumu', 'Aber',
            'Oneka', 'Awor', 'Odong', 'Akot', 'Olwoch', 'Achan', 'Okwir', 'Abalo'
        ];

        // Phone number prefixes for Uganda
        $phonePrefix = ['0701', '0702', '0703', '0704', '0705', '0706', '0707', '0708', '0709',
                       '0750', '0751', '0752', '0753', '0754', '0755', '0756', '0757', '0758', '0759',
                       '0770', '0771', '0772', '0773', '0774', '0775', '0776', '0777', '0778', '0779',
                       '0780', '0781', '0782', '0783', '0784', '0785', '0786', '0787', '0788', '0789'];

        $this->command->info('Creating 350 polling stations in Oyam District...');

        DB::beginTransaction();

        try {
            $stationCount = 0;
            $targetStations = 350;

            foreach ($locations as $subcounty => $parishes) {
                if ($stationCount >= $targetStations) break;

                // Calculate how many stations per subcounty (roughly equal distribution)
                $stationsPerSubcounty = ceil(($targetStations - $stationCount) / (count($locations) - array_search($subcounty, array_keys($locations))));
                $stationsPerSubcounty = min($stationsPerSubcounty, 40); // Max 40 per subcounty

                for ($i = 1; $i <= $stationsPerSubcounty && $stationCount < $targetStations; $i++) {
                    // Generate random coordinates within Oyam bounds
                    $latitude = $this->randomFloat($oyamBounds['minLat'], $oyamBounds['maxLat'], 6);
                    $longitude = $this->randomFloat($oyamBounds['minLng'], $oyamBounds['maxLng'], 6);

                    // Select random parish
                    $parish = $parishes[array_rand($parishes)];
                    $village = $parish . ' Village ' . chr(65 + ($i % 5)); // A, B, C, D, E

                    // Create polling station
                    $pollingStation = PollingStation::create([
                        'name' => $subcounty . ' P/S ' . str_pad($i, 3, '0', STR_PAD_LEFT),
                        'district' => 'Oyam',
                        'county' => $this->getCounty($subcounty),
                        'subcounty' => $subcounty,
                        'parish' => $parish,
                        'village' => $village,
                        'latitude' => $latitude,
                        'longitude' => $longitude,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    // Create user for the agent
                    $firstName = $firstNames[array_rand($firstNames)];
                    $lastName = $lastNames[array_rand($lastNames)];
                    $phoneNumber = $phonePrefix[array_rand($phonePrefix)] . rand(100000, 999999);
                    $fullName = $firstName . ' ' . $lastName;

                    $user = User::create([
                        'name' => $fullName,
                        'phone_number' => $phoneNumber,
                        'user_type' => 'agent',
                        'password' => Hash::make('password123'), // Default password
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    // Create agent linked to the user and polling station
                    Agent::create([
                        'user_id' => $user->id,
                        'polling_station_id' => $pollingStation->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    $stationCount++;

                    if ($stationCount % 50 == 0) {
                        $this->command->info("Created {$stationCount} polling stations...");
                    }
                }
            }

            DB::commit();
            $this->command->info("Successfully created {$stationCount} polling stations in Oyam District with agents!");

        } catch (\Exception $e) {
            DB::rollback();
            $this->command->error('Error creating polling stations: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Generate random float with specified decimal places
     */
    private function randomFloat($min, $max, $decimals = 6): float
    {
        $scale = pow(10, $decimals);
        return mt_rand($min * $scale, $max * $scale) / $scale;
    }

    /**
     * Get county for subcounty
     */
    private function getCounty($subcounty): string
    {
        $counties = [
            'Acaba' => 'Oyam North',
            'Aber' => 'Oyam South',
            'Ajuri' => 'Oyam North',
            'Anyeke' => 'Oyam South',
            'Iceme' => 'Oyam North',
            'Kamdini' => 'Oyam South',
            'Minakulu' => 'Oyam North',
            'Myene' => 'Oyam South',
            'Ngai' => 'Oyam North',
            'Otuke' => 'Oyam South',
            'Oyam' => 'Oyam Central',
        ];

        return $counties[$subcounty] ?? 'Oyam Central';
    }
}
