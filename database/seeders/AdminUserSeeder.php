<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the admin role
        $adminRole = Role::where('name', 'admin')->first();

        if (!$adminRole) {
            $this->command->error('Admin role not found. Please run RoleSeeder first.');
            return;
        }

        // Create admin user
        $adminUser = User::updateOrCreate(
            ['phone_number' => '+256700000000'],
            [
                'name' => 'System Administrator',
                'phone_number' => '+256700000000',
                'password' => Hash::make('admin123'),
                'user_type' => 'admin',
                'role_id' => $adminRole->id,
                'is_active' => true,
                'email_verified_at' => now()
            ]
        );

        // Assign admin role to user (both primary and additional)
        $adminUser->roles()->syncWithoutDetaching([$adminRole->id]);

        $this->command->info('Admin user created successfully!');
        $this->command->info('Phone: +256700000000');
        $this->command->info('Password: admin123');
        $this->command->warn('Please change the default password after first login.');
    }
}
