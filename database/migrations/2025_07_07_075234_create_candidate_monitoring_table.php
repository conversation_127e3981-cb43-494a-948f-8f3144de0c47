<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('candidate_monitoring', function (Blueprint $table) {
            $table->id();
            $table->foreignId('candidate_id')->constrained()->onDelete('cascade');
            $table->boolean('is_preferred')->default(false);
            $table->text('monitoring_notes')->nullable();
            $table->integer('target_votes')->nullable();
            $table->integer('vote_gap_alert_threshold')->nullable()->comment('Alert when vote gap is less than this value');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('candidate_monitoring');
    }
};
