<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the user_type enum to include all user types
        DB::statement("ALTER TABLE users MODIFY COLUMN user_type ENUM('admin', 'agent', 'manager', 'viewer') NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum values
        DB::statement("ALTER TABLE users MODIFY COLUMN user_type ENUM('admin', 'agent') NOT NULL");
    }
};
