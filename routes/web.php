<?php

use App\Http\Controllers\AgentController;
use App\Http\Controllers\CandidateController;
use App\Http\Controllers\EveidenceController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PollingStationController;
use App\Http\Controllers\PositionController;
use App\Http\Controllers\SpoiledVoteController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\VoteController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
 

Route::get('/', function () {

    if(Auth::guest())

        return view('auth.login');

    return redirect('home');

});

Auth::routes();

Route::group(['middleware' => ['auth']], function () {

    Route::get('/home', [HomeController::class, 'index'])->name('home');
    Route::get('/vote-trends-data/{positionId?}', [HomeController::class, 'getVoteTrendsData'])->name('vote.trends.data');
    Route::get('/api/polling-stations-map-data', [PollingStationController::class, 'getMapData'])->name('polling.stations.map.data');
    Route::resource('positions',PositionController::class);
    Route::resource('candidates',CandidateController::class);
    Route::resource('polling_stations',PollingStationController::class);
    Route::resource('agents',AgentController::class);
    Route::resource('votes',VoteController::class);
    Route::resource('evedence',EveidenceController::class);
    Route::resource('users',UserController::class);
    Route::resource('spoiled_votes',SpoiledVoteController::class);
    
    Route::get('/agent_dashboard', [HomeController::class, 'agentDashboard'])->name('agent_dashboard');
    Route::get('/ajax_get_candidates/{postion_id}', [PositionController::class, 'ajaxGetCandidates'])->name('ajax_get_candidates');
    
    // Candidate Monitoring Routes
    Route::get('/monitoring', [\App\Http\Controllers\CandidateMonitoringController::class, 'index'])->name('monitoring.index');
    Route::post('/monitoring/add/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'addPreferred'])->name('monitoring.add');
    Route::delete('/monitoring/remove/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'removePreferred'])->name('monitoring.remove');
    Route::post('/monitoring/toggle/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'togglePreferred'])->name('monitoring.toggle');
    Route::post('/monitoring/settings/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'updateSettings'])->name('monitoring.settings');
    Route::get('/monitoring/compare/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'compare'])->name('monitoring.compare');
    Route::get('/monitoring/guide', function() { return view('monitoring.guide'); })->name('monitoring.guide');
    
    // Notification routes
    Route::get('/notifications', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{id}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/read-all', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.read-all');

});
