/**
 * Enhanced Smooth Refresh Features
 * Additional utilities for smooth data updates
 */

// Add data attributes to elements for easier targeting
document.addEventListener('DOMContentLoaded', function() {
    // Add data attributes to statistics cards
    addDataAttributesToStats();
    
    // Add smooth transition classes
    addSmoothTransitions();
    
    // Setup connection status indicator
    setupConnectionStatus();
});

function addDataAttributesToStats() {
    // Dashboard statistics
    const statsMapping = {
        'Total Votes': 'total-votes',
        'Candidates': 'total-candidates', 
        'Positions': 'total-positions',
        'Polling Stations': 'total-stations',
        'Stations': 'total-stations',
        'Results': 'stations-with-results'
    };
    
    // Find and mark statistics cards
    document.querySelectorAll('.quick-stat, .stat-card, .card').forEach(card => {
        const text = card.textContent.toLowerCase();
        
        Object.entries(statsMapping).forEach(([key, value]) => {
            if (text.includes(key.toLowerCase())) {
                card.setAttribute('data-stat', value);
            }
        });
    });
    
    // Monitoring statistics
    const monitoringMapping = {
        'leading': 'leading-count',
        'trailing': 'trailing-count', 
        'alerts': 'alerts-count'
    };
    
    document.querySelectorAll('.monitoring-stat').forEach(card => {
        const text = card.textContent.toLowerCase();
        
        Object.entries(monitoringMapping).forEach(([key, value]) => {
            if (text.includes(key)) {
                card.setAttribute('data-stat', value);
            }
        });
    });
}

function addSmoothTransitions() {
    // Add CSS for smooth transitions
    const style = document.createElement('style');
    style.textContent = `
        .smooth-update {
            transition: all 0.3s ease;
        }
        
        .updated {
            background-color: rgba(40, 167, 69, 0.1) !important;
            border-left: 3px solid #28a745;
            transition: all 0.3s ease;
        }
        
        .stat-value, .fw-bold {
            transition: color 0.3s ease;
        }
        
        .number-updating {
            color: #007bff;
            font-weight: bold;
        }
        
        .connection-status {
            position: fixed;
            bottom: 20px;
            left: 20px;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 9998;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            pointer-events: none;
        }
        
        .connection-status.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .connection-status.online {
            background: rgba(40, 167, 69, 0.9);
            color: white;
        }
        
        .connection-status.offline {
            background: rgba(220, 53, 69, 0.9);
            color: white;
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .slide-in {
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { 
                opacity: 0;
                transform: translateY(-10px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);
    
    // Add smooth-update class to relevant elements
    document.querySelectorAll('.card, .quick-stat, .candidate-card').forEach(el => {
        el.classList.add('smooth-update');
    });
}

function setupConnectionStatus() {
    const statusIndicator = document.createElement('div');
    statusIndicator.className = 'connection-status';
    statusIndicator.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="status-dot me-2" style="width: 8px; height: 8px; border-radius: 50%; background: currentColor;"></div>
            <span class="status-text">Connected</span>
        </div>
    `;
    document.body.appendChild(statusIndicator);
    
    // Monitor connection status
    function updateConnectionStatus() {
        if (navigator.onLine) {
            statusIndicator.className = 'connection-status online';
            statusIndicator.querySelector('.status-text').textContent = 'Connected';
        } else {
            statusIndicator.className = 'connection-status offline show';
            statusIndicator.querySelector('.status-text').textContent = 'Offline';
        }
    }
    
    // Show status briefly on page load
    setTimeout(() => {
        statusIndicator.classList.add('show');
        setTimeout(() => statusIndicator.classList.remove('show'), 3000);
    }, 1000);
    
    // Listen for connection changes
    window.addEventListener('online', () => {
        updateConnectionStatus();
        statusIndicator.classList.add('show');
        setTimeout(() => statusIndicator.classList.remove('show'), 3000);
    });
    
    window.addEventListener('offline', () => {
        updateConnectionStatus();
        statusIndicator.classList.add('show');
    });
}

// Enhanced number animation with visual feedback
function enhancedAnimateNumber(element, targetNumber, duration = 1000) {
    if (!element) return;
    
    const currentNumber = parseInt(element.textContent) || 0;
    const difference = targetNumber - currentNumber;
    
    if (difference === 0) return;
    
    // Add visual feedback
    element.classList.add('number-updating');
    
    const startTime = Date.now();
    const startNumber = currentNumber;
    
    function updateNumber() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Use easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.round(startNumber + (difference * easeOutQuart));
        
        element.textContent = current.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            element.classList.remove('number-updating');
            
            // Add brief highlight if number increased
            if (difference > 0) {
                element.style.color = '#28a745';
                setTimeout(() => element.style.color = '', 1000);
            }
        }
    }
    
    requestAnimationFrame(updateNumber);
}

// Utility function to show toast notifications
function showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 14px;
        z-index: 10000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // Animate out and remove
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => document.body.removeChild(toast), 300);
    }, duration);
}

// Export functions for use by the main refresh system
window.smoothRefreshUtils = {
    enhancedAnimateNumber,
    showToast,
    addDataAttributesToStats,
    addSmoothTransitions
};
