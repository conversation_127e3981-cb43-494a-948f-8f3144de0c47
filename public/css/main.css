    :root {
            --primary-color: #FFA500;
            --primary-light: #FFD700;
            --primary-dark: #FF8C00;
            --text-dark: #333;
            --text-light: #6c757d;
            --white: #fff;
            --light-bg: #f8f9fa;
            --border-radius: 10px;
            --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            color: var(--text-dark);
        }
        
        /* Navbar Styling - Compact Version */
        .navbar-custom {
            background: #fff;
            box-shadow: 0 1px 8px rgba(0, 0, 0, 0.04);
            padding: 0.4rem 1rem;
            min-height: 50px;
        }

        .navbar-brand-custom {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.2rem;
            color: var(--primary-color) !important;
        }

        .navbar-brand-custom .brand-icon {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            box-shadow: 0 2px 6px rgba(255, 165, 0, 0.15);
            font-size: 0.9rem;
        }
        
        .nav-link-custom {
            color: var(--text-dark);
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            transition: all 0.2s ease;
            margin: 0 2px;
            position: relative;
            font-size: 0.9rem;
        }

        .nav-link-custom:hover, .nav-link-custom.active {
            color: var(--primary-color);
            background-color: rgba(255, 165, 0, 0.08);
        }

        .nav-link-custom.active::after {
            content: '';
            position: absolute;
            bottom: 3px;
            left: 50%;
            transform: translateX(-50%);
            width: 16px;
            height: 2px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            border-radius: 6px;
        }

        .nav-link-custom i {
            margin-right: 5px;
            font-size: 0.85rem;
        }
        
        .navbar-toggler-custom {
            border: none;
            padding: 0;
        }
        
        .navbar-toggler-custom:focus {
            box-shadow: none;
        }
        
        .navbar-toggler-icon-custom {
            background-color: var(--primary-color);
            display: block;
            width: 22px;
            height: 2px;
            border-radius: 1px;
            position: relative;
            transition: all 0.3s;
        }
        
        .navbar-toggler-icon-custom::before,
        .navbar-toggler-icon-custom::after {
            content: '';
            background-color: var(--primary-color);
            display: block;
            width: 22px;
            height: 2px;
            border-radius: 1px;
            position: absolute;
            transition: all 0.3s;
        }
        
        .navbar-toggler-icon-custom::before {
            top: -6px;
        }
        
        .navbar-toggler-icon-custom::after {
            bottom: -6px;
        }
        
        /* User dropdown - Compact */
        .user-dropdown {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, rgba(255, 165, 0, 0.08) 0%, rgba(255, 215, 0, 0.08) 100%);
            border-radius: 20px;
            padding: 3px 10px 3px 3px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .user-dropdown:hover {
            background: linear-gradient(135deg, rgba(255, 165, 0, 0.12) 0%, rgba(255, 215, 0, 0.12) 100%);
        }

        .user-avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 6px;
            font-size: 0.8rem;
        }

        .user-name {
            font-weight: 500;
            color: var(--text-dark);
            margin-right: 4px;
            font-size: 0.85rem;
        }
        
        .dropdown-menu-custom {
            border: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            padding: 0.4rem 0;
            min-width: 180px;
            margin-top: 6px;
        }

        .dropdown-item-custom {
            padding: 0.5rem 1rem;
            font-weight: 500;
            color: var(--text-dark);
            display: flex;
            align-items: center;
            transition: all 0.2s;
            font-size: 0.85rem;
        }

        .dropdown-item-custom i {
            margin-right: 8px;
            font-size: 0.9rem;
            width: 16px;
        }

        .dropdown-item-custom:hover {
            background-color: rgba(255, 165, 0, 0.08);
            color: var(--primary-color);
        }
        
        .dropdown-item-custom.logout {
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            color: #dc3545;
        }
        
        .dropdown-item-custom.logout:hover {
            background-color: rgba(220, 53, 69, 0.05);
        }
        
        /* Main content area - Compact */
        .main-content {
            padding: 1.5rem 0;
        }
        
        /* Alert styling */
        .alert-custom {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--box-shadow);
        }
        
        /* Compact navbar additions */
        .navbar-nav .nav-item {
            margin: 0 1px;
        }

        .navbar-nav .dropdown-toggle::after {
            font-size: 0.7rem;
            margin-left: 0.3rem;
        }

        /* Notification bell styling */
        #notificationDropdown {
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        #notificationDropdown:hover {
            background-color: rgba(255, 165, 0, 0.08);
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .navbar-collapse {
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                padding: 0.8rem;
                margin-top: 0.8rem;
            }

            .nav-link-custom.active::after {
                display: none;
            }

            .user-dropdown {
                margin-top: 0.8rem;
                justify-content: center;
            }

            .navbar-custom {
                padding: 0.5rem 1rem;
            }
        }

        @media (max-width: 768px) {
            .navbar-brand-custom {
                font-size: 1.1rem;
            }

            .navbar-brand-custom .brand-icon {
                width: 28px;
                height: 28px;
                font-size: 0.8rem;
            }

            .user-name {
                display: none !important;
            }
        }