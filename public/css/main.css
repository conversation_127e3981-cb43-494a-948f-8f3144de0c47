    :root {
            --primary-color: #FFA500;
            --primary-light: #FFD700;
            --primary-dark: #FF8C00;
            --text-dark: #333;
            --text-light: #6c757d;
            --white: #fff;
            --light-bg: #f8f9fa;
            --border-radius: 10px;
            --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            color: var(--text-dark);
        }
        
        /* Navbar Styling */
        .navbar-custom {
            background: #fff;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1rem;
        }
        
        .navbar-brand-custom {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .navbar-brand-custom .brand-icon {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            box-shadow: 0 3px 10px rgba(255, 165, 0, 0.2);
        }
        
        .nav-link-custom {
            color: var(--text-dark);
            font-weight: 500;
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            margin: 0 5px;
            position: relative;
        }
        
        .nav-link-custom:hover, .nav-link-custom.active {
            color: var(--primary-color);
            background-color: rgba(255, 165, 0, 0.05);
        }
        
        .nav-link-custom.active::after {
            content: '';
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 3px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            border-radius: 10px;
        }
        
        .nav-link-custom i {
            margin-right: 7px;
        }
        
        .navbar-toggler-custom {
            border: none;
            padding: 0;
        }
        
        .navbar-toggler-custom:focus {
            box-shadow: none;
        }
        
        .navbar-toggler-icon-custom {
            background-color: var(--primary-color);
            display: block;
            width: 22px;
            height: 2px;
            border-radius: 1px;
            position: relative;
            transition: all 0.3s;
        }
        
        .navbar-toggler-icon-custom::before,
        .navbar-toggler-icon-custom::after {
            content: '';
            background-color: var(--primary-color);
            display: block;
            width: 22px;
            height: 2px;
            border-radius: 1px;
            position: absolute;
            transition: all 0.3s;
        }
        
        .navbar-toggler-icon-custom::before {
            top: -6px;
        }
        
        .navbar-toggler-icon-custom::after {
            bottom: -6px;
        }
        
        /* User dropdown */
        .user-dropdown {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, rgba(255, 165, 0, 0.1) 0%, rgba(255, 215, 0, 0.1) 100%);
            border-radius: 30px;
            padding: 5px 15px 5px 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .user-dropdown:hover {
            background: linear-gradient(135deg, rgba(255, 165, 0, 0.15) 0%, rgba(255, 215, 0, 0.15) 100%);
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 10px;
        }
        
        .user-name {
            font-weight: 500;
            color: var(--text-dark);
            margin-right: 5px;
        }
        
        .dropdown-menu-custom {
            border: none;
            box-shadow: var(--box-shadow);
            border-radius: var(--border-radius);
            padding: 0.5rem 0;
            min-width: 200px;
            margin-top: 10px;
        }
        
        .dropdown-item-custom {
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            color: var(--text-dark);
            display: flex;
            align-items: center;
            transition: all 0.2s;
        }
        
        .dropdown-item-custom i {
            margin-right: 10px;
            font-size: 1.1rem;
        }
        
        .dropdown-item-custom:hover {
            background-color: rgba(255, 165, 0, 0.05);
            color: var(--primary-color);
        }
        
        .dropdown-item-custom.logout {
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            color: #dc3545;
        }
        
        .dropdown-item-custom.logout:hover {
            background-color: rgba(220, 53, 69, 0.05);
        }
        
        /* Main content area */
        .main-content {
            padding: 2rem 0;
        }
        
        /* Alert styling */
        .alert-custom {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--box-shadow);
        }
        
        /* Responsive adjustments */
        @media (max-width: 992px) {
            .navbar-collapse {
                background-color: white;
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                padding: 1rem;
                margin-top: 1rem;
            }
            
            .nav-link-custom.active::after {
                display: none;
            }
            
            .user-dropdown {
                margin-top: 1rem;
                justify-content: center;
            }
        }