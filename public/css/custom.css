  /* Dashboard Styles */
    .dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .dashboard-title {
        font-size: 1.75rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .dashboard-subtitle {
        color: #6c757d;
        font-size: 0.95rem;
    }
    
    .stats-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }
    
    .stats-card .card-body {
        padding: 1.5rem;
    }
    
    .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 10px rgba(255, 165, 0, 0.3);
    }
    
    .stats-title {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .stats-value {
        font-size: 1.75rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }
    
    .stats-desc {
        font-size: 0.85rem;
        color: #6c757d;
    }
    
    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.25rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
        margin-left: 0.5rem;
    }
    
    .section-icon {
        color: #FFA500;
        font-size: 1.25rem;
    }
    
    .results-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .results-header {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        color: white;
        padding: 1rem 1.5rem;
        font-weight: 500;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .results-body {
        padding: 1.5rem;
    }
    
    .progress-bar-custom {
        height: 8px;
        border-radius: 4px;
        margin-top: 0.5rem;
        margin-bottom: 1rem;
        background-color: #e9ecef;
    }
    
    .progress-fill {
        height: 100%;
        border-radius: 4px;
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
    }
    
    .candidate-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .candidate-row:last-child {
        border-bottom: none;
    }
    
    .candidate-info {
        display: flex;
        align-items: center;
    }
    
    .candidate-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 1rem;
        border: 2px solid #FFA500;
    }
    
    .candidate-name {
        font-weight: 500;
        color: #333;
    }
    
    .candidate-party {
        font-size: 0.8rem;
        color: #6c757d;
    }
    
    .candidate-votes {
        text-align: right;
    }
    
    .votes-count {
        font-weight: 600;
        color: #333;
        font-size: 1.1rem;
    }
    
    .votes-percent {
        font-size: 0.85rem;
        color: #6c757d;
        font-weight: 500;
    }
    
    .total-votes-row {
        display: flex;
        justify-content: space-between;
        padding-top: 1rem;
        margin-top: 1rem;
        border-top: 1px dashed rgba(0, 0, 0, 0.1);
        font-weight: 600;
    }
    
    /* Compact Stats Cards */
    .stats-card-small {
        border: none;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }
    
    .stats-card-small:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    
    .stats-icon-small {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
        box-shadow: 0 2px 5px rgba(255, 165, 0, 0.3);
    }
    
    .stats-title-small {
        font-size: 0.75rem;
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 0;
    }
    
    .stats-value-small {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
    }
    
    /* Compact Results Cards */
    .results-card-compact {
        border: none;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        margin-bottom: 1rem;
    }
    
    .candidate-row-compact {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.4rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .candidate-row-compact:last-child {
        border-bottom: none;
    }
    
    .candidate-avatar-small {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 0.75rem;
        border: 1px solid #FFA500;
    }
    
    .candidate-name-small {
        font-weight: 500;
        color: #333;
        font-size: 0.85rem;
    }
    
    .votes-count-small {
        font-weight: 600;
        color: #333;
        font-size: 0.85rem;
    }
    
    .votes-percent-small {
        font-size: 0.75rem;
        color: #6c757d;
        font-weight: 500;
    }
    
    .progress-bar-custom-small {
        height: 5px;
        border-radius: 3px;
        margin-top: 0.25rem;
        margin-bottom: 0.75rem;
        background-color: #e9ecef;
    }
    
    .small-text {
        font-size: 0.75rem;
    }
    
    .small-icon {
        font-size: 0.75rem;
    }
    
    /* Polling Station Filters */
    .polling-station-filters {
        width: 400px;
    }
    
    .filter-btn {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        color: white;
        border: none;
        box-shadow: 0 2px 5px rgba(255, 165, 0, 0.3);
        transition: all 0.2s;
    }
    
    .filter-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(255, 165, 0, 0.4);
        color: white;
    }
    
    .station-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
        overflow: hidden;
    }
    
    .station-header {
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .station-name {
        font-weight: 600;
        color: #333;
        margin: 0;
        display: flex;
        align-items: center;
    }
    
    .station-name i {
        color: #FFA500;
        margin-right: 0.5rem;
    }
    
    .station-body {
        padding: 1.5rem;
    }
    
    .agent-info {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 1rem;
    }
    
    .agent-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
    }
    
    .agent-icon i {
        color: #FFA500;
        font-size: 1.25rem;
    }
    
    .agent-details {
        flex: 1;
    }
    
    .agent-name {
        font-weight: 500;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .agent-phone {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .evidence-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        margin-top: 1rem;
    }
    
    .evidence-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 0.75rem;
        background-color: #f8f9fa;
        border-radius: 6px;
        font-size: 0.9rem;
        color: #495057;
        transition: all 0.2s;
    }
    
    .evidence-item:hover {
        background-color: #e9ecef;
        color: #212529;
        text-decoration: none;
    }
    
    .evidence-item i {
        margin-right: 0.5rem;
        color: #FFA500;
    }


     .btn-add {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        border: none;
        color: white;
        font-weight: 500;
        padding: 0.6rem 1.25rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 10px rgba(255, 165, 0, 0.2);
        transition: all 0.3s;
    }

      .form-control-custom:focus {
        border-color: #FFA500;
        box-shadow: 0 0 0 0.25rem rgba(255, 165, 0, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        border: none;
        color: white;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(255, 165, 0, 0.3);
    }
    
    .btn-cancel {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #6c757d;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s;
    }
    
    .btn-cancel:hover {
        background-color: #e9ecef;
    }